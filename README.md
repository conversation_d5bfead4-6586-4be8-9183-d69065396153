# Auto Retopology Addon for <PERSON>lender

A powerful Blender addon that provides instant retopology with multiple algorithms for mesh optimization. This addon is designed to automatically create clean, quad-based topology from high-polygon meshes with just one click.

## Features

### Multiple Retopology Algorithms
- **Quad Remesh**: Uses <PERSON>lender's built-in quad remesher for clean, uniform topology
- **Voxel Remesh**: Voxel-based remeshing perfect for organic shapes
- **Edge Flow**: Edge-flow aware retopology that follows natural contours
- **Adaptive**: Adaptive density based on surface curvature

### Key Capabilities
- ✅ One-click retopology with intelligent defaults
- ✅ Configurable target face count
- ✅ Preserve sharp edges and corners
- ✅ Automatic mesh quality analysis
- ✅ Real-time quality metrics display
- ✅ Quick presets for common use cases
- ✅ Post-processing cleanup and optimization

### Quality Analysis
- Quad ratio calculation
- Triangle and N-gon counting
- Edge flow quality scoring
- Aspect ratio analysis
- Edge length consistency metrics

## Installation

1. Download the addon files to a folder named `auto_retopology`
2. Zip the folder or copy it to your Blender addons directory
3. In Blender, go to `Edit > Preferences > Add-ons`
4. Click `Install...` and select the zip file, or enable the addon if copied directly
5. Enable "Auto Retopology" in the addon list
6. The addon panel will appear in the 3D Viewport sidebar under the "Retopo" tab

## Usage

### Basic Workflow

1. **Select your high-poly mesh** in Object mode
2. **Open the Retopo panel** in the 3D Viewport sidebar (N key)
3. **Choose your algorithm** and settings:
   - **Quad Remesh**: Best for hard surface models
   - **Voxel Remesh**: Best for organic/sculpted models
   - **Edge Flow**: Best when preserving natural flow is important
   - **Adaptive**: Best for mixed geometry with varying detail levels

4. **Set target face count** (default: 1000 faces)
5. **Click "Retopologize"** to process your mesh
6. **Analyze quality** using the "Analyze Quality" button

### Quick Presets

- **Low Poly**: 500 faces, optimized for game assets
- **High Detail**: 5000 faces, preserves more detail

### Algorithm-Specific Settings

#### Quad Remesh
- **Octree Depth**: Controls resolution (1-10, default: 4)
- **Scale**: Fine-tune the result (0.1-2.0, default: 0.99)

#### Voxel Remesh
- **Voxel Size**: Size of voxels (0.001-1.0, default: 0.1)
- **Adaptivity**: Reduces faces in flat areas (0.0-1.0, default: 0.0)

#### Edge Flow
- **Iterations**: Number of flow optimization passes (1-10, default: 3)

### Quality Control
- **Min/Max Edge Length**: Control edge length ratios
- **Preserve Sharp Edges**: Maintain hard edges and corners
- **Smooth Shading**: Apply smooth shading to result

## Tips for Best Results

### Mesh Preparation
- Ensure your input mesh is manifold (no holes or non-manifold geometry)
- Remove any loose vertices or edges
- The addon includes automatic cleanup, but clean input gives better results

### Algorithm Selection Guide
- **Quad Remesh**: 
  - ✅ Hard surface models, architectural geometry
  - ✅ Models with clear edge definition
  - ❌ Very organic/sculpted forms

- **Voxel Remesh**:
  - ✅ Organic shapes, sculpted models
  - ✅ Characters, creatures, natural forms
  - ❌ Hard surface details (may be lost)

- **Edge Flow**:
  - ✅ Character faces, body topology
  - ✅ When animation-friendly topology is needed
  - ❌ Very high-poly input (may be slow)

- **Adaptive**:
  - ✅ Mixed geometry with varying detail
  - ✅ When you want automatic density control
  - ❌ When uniform topology is required

### Target Face Count Guidelines
- **Game Assets**: 500-2000 faces
- **Animation Models**: 2000-8000 faces  
- **High Detail Visualization**: 5000-20000 faces
- **Background Objects**: 100-500 faces

## Quality Metrics Explained

- **Quad Ratio**: Percentage of 4-sided faces (higher is better for most uses)
- **Triangle Count**: Number of 3-sided faces
- **N-gon Count**: Number of faces with 5+ sides
- **Edge Flow Score**: How well edges follow natural contours (0-1, higher is better)
- **Overall Quality**: Combined quality score

## Troubleshooting

### Common Issues

**"Please select a mesh object"**
- Make sure you have a mesh selected in Object mode

**"Retopology failed"**
- Check that your input mesh is manifold
- Try reducing target face count
- Ensure the mesh has sufficient geometry

**Poor quality results**
- Try a different algorithm
- Adjust the target face count
- Check algorithm-specific settings
- Use "Analyze Quality" to identify issues

### Performance Tips
- For very high-poly meshes (>1M faces), consider decimating first
- Edge Flow algorithm is slower on complex meshes
- Voxel size greatly affects processing time

## Technical Details

### Requirements
- Blender 4.0 or higher
- Python 3.x (included with Blender)

### Dependencies
- bmesh (Blender's mesh editing module)
- mathutils (Blender's math utilities)
- numpy (for advanced calculations)

## Support

This addon provides professional-quality retopology results suitable for:
- Game development pipelines
- Animation and rigging workflows  
- 3D printing preparation
- Mesh optimization for real-time rendering

For best results, experiment with different algorithms and settings based on your specific mesh type and intended use case.
