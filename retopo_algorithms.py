"""
Retopology algorithms for Auto Retopology Addon
"""

import bpy
import bmesh
import mathutils
from mathutils import Vector
import numpy as np
import math

def preserve_original_shape(obj, original_mesh):
    """
    Preserve the original shape using a simple and reliable shrinkwrap method
    """
    try:
        print("Preserving original shape...")

        # Create a temporary object with the original mesh for shrinkwrap target
        temp_obj = bpy.data.objects.new("temp_original", original_mesh)
        bpy.context.collection.objects.link(temp_obj)

        # Use simple, reliable shrinkwrap settings
        shrinkwrap = obj.modifiers.new(name="ShapePreservation", type='SHRINKWRAP')
        shrinkwrap.target = temp_obj
        shrinkwrap.wrap_method = 'NEAREST_SURFACEPOINT'
        shrinkwrap.offset = 0.0  # No offset for exact surface matching

        # Apply the shrinkwrap modifier
        bpy.context.view_layer.objects.active = obj
        bpy.ops.object.modifier_apply(modifier=shrinkwrap.name)

        # Clean up temporary object
        bpy.context.collection.objects.unlink(temp_obj)
        bpy.data.objects.remove(temp_obj)

        print("✓ Shape preservation completed")

    except Exception as e:
        print(f"Shape preservation failed: {e}")
        # Continue without shape preservation if it fails



def quad_remesh(obj, props):
    """
    Perform quad-based remeshing using Blender's built-in remesh modifier
    Enhanced with automatic parameter optimization
    """
    try:
        from . import retopo_utils

        # Validate and prepare mesh
        is_valid, message = retopo_utils.validate_mesh(obj)
        if not is_valid:
            print(f"Mesh validation failed: {message}")
            retopo_utils.prepare_mesh_for_retopo(obj)

        # Calculate optimal octree depth based on target face count
        if props.target_face_count > 0:
            # Always use calculated depth to respect target face count
            octree_depth = retopo_utils.calculate_optimal_octree_depth(obj, props.target_face_count)
            print(f"Target faces: {props.target_face_count}, Calculated octree depth: {octree_depth}")
        else:
            # Only use user setting if no target face count is specified
            octree_depth = props.quad_octree_depth

        # Add remesh modifier
        modifier = obj.modifiers.new(name="AutoRetopo_Remesh", type='REMESH')
        modifier.mode = 'SHARP'
        modifier.octree_depth = octree_depth
        modifier.scale = props.quad_scale
        modifier.use_remove_disconnected = True
        modifier.use_smooth_shade = props.smooth_shading

        if props.preserve_sharp_edges:
            modifier.sharpness = 1.0
        else:
            modifier.sharpness = 0.5

        # Store original mesh for shape preservation (if enabled)
        if props.preserve_shape:
            original_mesh = obj.data.copy()
            original_mesh.name = obj.data.name + "_shape_reference"

        # Apply modifier
        bpy.context.view_layer.objects.active = obj
        bpy.ops.object.modifier_apply(modifier=modifier.name)

        # SHAPE PRESERVATION: Project vertices back to original surface (if enabled)
        if props.preserve_shape:
            preserve_original_shape(obj, original_mesh)
            # Clean up temporary mesh
            bpy.data.meshes.remove(original_mesh)

        # Post-process to improve quad topology
        improve_quad_topology(obj)

        return True

    except Exception as e:
        print(f"Quad remesh failed: {e}")
        return False

def voxel_remesh(obj, props):
    """
    Perform voxel-based remeshing for organic shapes
    Enhanced with automatic voxel size calculation
    """
    try:
        from . import retopo_utils

        # Validate and prepare mesh
        is_valid, message = retopo_utils.validate_mesh(obj)
        if not is_valid:
            print(f"Mesh validation failed: {message}")
            retopo_utils.prepare_mesh_for_retopo(obj)

        # Calculate optimal voxel size based on target face count
        if props.target_face_count > 0:
            # Always use calculated voxel size to respect target face count
            voxel_size = retopo_utils.calculate_optimal_voxel_size(obj, props.target_face_count)
            print(f"Target faces: {props.target_face_count}, Calculated voxel size: {voxel_size:.4f}")
        else:
            # Only use user setting if no target face count is specified
            voxel_size = props.voxel_size

        # Store original mesh for shape preservation (if enabled)
        if props.preserve_shape:
            original_mesh = obj.data.copy()
            original_mesh.name = obj.data.name + "_shape_reference"

        # Add voxel remesh modifier with shape-preserving settings
        modifier = obj.modifiers.new(name="AutoRetopo_Voxel", type='REMESH')
        modifier.mode = 'VOXEL'
        modifier.voxel_size = voxel_size

        # Use more conservative settings for shape preservation
        if props.preserve_shape:
            modifier.adaptivity = min(props.voxel_adaptivity, 0.1)  # Lower adaptivity preserves shape better
            modifier.use_smooth_shade = True  # Always smooth for better shape preservation
        else:
            modifier.adaptivity = props.voxel_adaptivity
            modifier.use_smooth_shade = props.smooth_shading

        # Store original bounds for validation
        original_bounds = retopo_utils.get_mesh_bounds(obj)
        original_size = original_bounds['size']

        # Apply modifier
        bpy.context.view_layer.objects.active = obj
        bpy.ops.object.modifier_apply(modifier=modifier.name)

        # VALIDATION: Check if the result is severely deformed
        new_bounds = retopo_utils.get_mesh_bounds(obj)
        new_size = new_bounds['size']

        # Calculate size ratios to detect deformation
        size_ratios = []
        for i in range(3):
            if new_size[i] > 0.001:  # Avoid division by zero
                ratio = max(new_size) / new_size[i]
                size_ratios.append(ratio)

        max_ratio = max(size_ratios) if size_ratios else 1.0

        print(f"Voxel remesh result validation:")
        print(f"  Original size: {original_size.x:.3f} x {original_size.y:.3f} x {original_size.z:.3f}")
        print(f"  New size: {new_size.x:.3f} x {new_size.y:.3f} x {new_size.z:.3f}")
        print(f"  Max dimension ratio: {max_ratio:.1f}")

        # If severely deformed (one dimension much larger than others), try to fix
        if max_ratio > 20:
            print("⚠ WARNING: Severe deformation detected! Attempting recovery...")

            # Try with a much larger voxel size
            recovery_voxel_size = voxel_size * 5.0
            print(f"  Trying recovery with voxel size: {recovery_voxel_size:.4f}")

            # Restore original mesh and try again
            if props.preserve_shape:
                obj.data = original_mesh.copy()

            # Apply voxel remesh with larger voxel size
            recovery_modifier = obj.modifiers.new(name="RecoveryVoxel", type='REMESH')
            recovery_modifier.mode = 'VOXEL'
            recovery_modifier.voxel_size = recovery_voxel_size
            recovery_modifier.adaptivity = 0.0  # No adaptivity for recovery
            recovery_modifier.use_smooth_shade = True

            bpy.ops.object.modifier_apply(modifier=recovery_modifier.name)

            # Check recovery result
            recovery_bounds = retopo_utils.get_mesh_bounds(obj)
            recovery_size = recovery_bounds['size']
            recovery_ratios = []
            for i in range(3):
                if recovery_size[i] > 0.001:
                    ratio = max(recovery_size) / recovery_size[i]
                    recovery_ratios.append(ratio)

            recovery_max_ratio = max(recovery_ratios) if recovery_ratios else 1.0
            print(f"  Recovery result ratio: {recovery_max_ratio:.1f}")

            if recovery_max_ratio < max_ratio:
                print("✓ Recovery successful!")
            else:
                print("✗ Recovery failed, but continuing...")

        # SHAPE PRESERVATION: Project vertices back to original surface (if enabled)
        if props.preserve_shape:
            preserve_original_shape(obj, original_mesh)
            # Clean up temporary mesh
            bpy.data.meshes.remove(original_mesh)

        # Post-process for better topology
        optimize_voxel_topology(obj, props)

        return True

    except Exception as e:
        print(f"Voxel remesh failed: {e}")
        return False

def edge_flow_retopo(obj, props):
    """
    Perform edge-flow aware retopology that follows natural contours
    Enhanced with curvature analysis and flow optimization
    """
    try:
        from . import retopo_utils

        # Validate and prepare mesh
        is_valid, message = retopo_utils.validate_mesh(obj)
        if not is_valid:
            print(f"Mesh validation failed: {message}")
            retopo_utils.prepare_mesh_for_retopo(obj)

        # First apply a base remesh to get reasonable topology
        base_success = quad_remesh(obj, props)
        if not base_success:
            return False

        # Get mesh data for edge flow optimization
        mesh = obj.data
        bm = bmesh.new()
        bm.from_mesh(mesh)

        # Ensure face indices are valid
        bm.faces.ensure_lookup_table()
        bm.edges.ensure_lookup_table()
        bm.verts.ensure_lookup_table()

        # Adjust face count to match target if needed
        current_faces = len(bm.faces)
        target_faces = props.target_face_count

        print(f"Edge Flow: Current faces: {current_faces}, Target faces: {target_faces}")

        if target_faces > 0 and abs(current_faces - target_faces) > target_faces * 0.1:
            # Significant difference, adjust face count
            if current_faces > target_faces:
                # Decimate to reduce face count
                decimate_ratio = target_faces / current_faces
                bmesh.ops.decimate_collapse(bm, edges=bm.edges, ratio=decimate_ratio)
                print(f"Edge Flow: Decimated to ratio {decimate_ratio:.3f}")
            elif current_faces < target_faces * 0.8:
                # Subdivide to increase face count
                subdivisions = max(1, int(math.log2(target_faces / current_faces)))
                bmesh.ops.subdivide_edges(bm, edges=bm.edges, cuts=subdivisions, use_grid_fill=True)
                print(f"Edge Flow: Subdivided with {subdivisions} cuts")

        # Recalculate after face count adjustment
        bm.faces.ensure_lookup_table()
        bm.edges.ensure_lookup_table()
        bm.verts.ensure_lookup_table()

        # Calculate curvature and flow directions
        curvatures = calculate_vertex_curvature(bm)
        flow_directions = calculate_flow_directions(bm)

        # Perform iterative edge flow optimization
        for iteration in range(props.edge_flow_iterations):
            optimize_edge_flow_advanced(bm, curvatures, flow_directions)

            # Recalculate flow directions every few iterations
            if iteration % 2 == 0:
                flow_directions = calculate_flow_directions(bm)

        # Final topology cleanup
        cleanup_edge_flow_topology(bm)

        # Update mesh
        bm.to_mesh(mesh)
        bm.free()

        return True

    except Exception as e:
        print(f"Edge flow retopo failed: {e}")
        return False

def adaptive_retopo(obj, props):
    """
    Perform adaptive retopology with density based on curvature and detail areas
    Enhanced with multi-pass adaptive refinement
    """
    try:
        from . import retopo_utils

        # Validate and prepare mesh
        is_valid, message = retopo_utils.validate_mesh(obj)
        if not is_valid:
            print(f"Mesh validation failed: {message}")
            retopo_utils.prepare_mesh_for_retopo(obj)

        # First, apply voxel remesh as base with conservative settings
        base_props = type('Props', (), {})()
        for attr in dir(props):
            if not attr.startswith('_'):
                setattr(base_props, attr, getattr(props, attr))

        # Use larger voxel size for base mesh
        base_props.voxel_size = props.voxel_size * 1.5
        base_props.target_face_count = int(props.target_face_count * 0.7)

        if not voxel_remesh(obj, base_props):
            return False

        # Get mesh data for adaptive refinement
        mesh = obj.data
        bm = bmesh.new()
        bm.from_mesh(mesh)

        # Ensure face indices are valid
        bm.faces.ensure_lookup_table()
        bm.edges.ensure_lookup_table()
        bm.verts.ensure_lookup_table()

        # Multi-pass adaptive refinement
        target_faces = props.target_face_count
        current_faces = len(bm.faces)

        print(f"Adaptive: After base voxel remesh: {current_faces} faces, Target: {target_faces}")

        # Pass 1: Curvature-based adaptation
        curvature_map = calculate_adaptive_curvature_map(bm)

        if current_faces > target_faces:
            # Decimate based on curvature and importance
            decimate_ratio = target_faces / current_faces
            print(f"Adaptive: Decimating with ratio {decimate_ratio:.3f}")
            adaptive_decimate_advanced(bm, decimate_ratio, curvature_map)
        elif current_faces < target_faces * 0.8:
            # Subdivide high-importance areas
            faces_to_add = target_faces - current_faces
            print(f"Adaptive: Subdividing to add ~{faces_to_add} faces")
            adaptive_subdivide_advanced(bm, faces_to_add, curvature_map)

        # Pass 2: Edge flow optimization
        flow_directions = calculate_flow_directions(bm)
        optimize_adaptive_flow(bm, curvature_map, flow_directions)

        # Pass 3: Final cleanup and quality improvement
        finalize_adaptive_topology(bm, props)

        # Update mesh
        bm.to_mesh(mesh)
        bm.free()

        return True

    except Exception as e:
        print(f"Adaptive retopo failed: {e}")
        return False

def calculate_vertex_curvature(bm):
    """
    Calculate curvature at each vertex using local geometry
    """
    curvatures = {}
    
    for vert in bm.verts:
        if len(vert.link_edges) < 3:
            curvatures[vert.index] = 0.0
            continue
        
        # Calculate mean curvature using adjacent vertices
        center = vert.co
        neighbors = [edge.other_vert(vert).co for edge in vert.link_edges]
        
        if len(neighbors) < 3:
            curvatures[vert.index] = 0.0
            continue
        
        # Calculate average neighbor position
        avg_neighbor = sum(neighbors, Vector()) / len(neighbors)
        
        # Calculate curvature as distance from center to average neighbor
        curvature = (center - avg_neighbor).length
        curvatures[vert.index] = curvature
    
    return curvatures

def optimize_edge_flow(bm, curvatures):
    """
    Optimize edge flow by adjusting vertex positions
    """
    # Smooth vertices based on curvature
    for vert in bm.verts:
        if len(vert.link_edges) < 2:
            continue
        
        curvature = curvatures.get(vert.index, 0.0)
        smooth_factor = max(0.1, 1.0 - curvature * 2.0)  # Less smoothing in high curvature areas
        
        # Calculate smoothed position
        neighbors = [edge.other_vert(vert).co for edge in vert.link_edges]
        if neighbors:
            avg_pos = sum(neighbors, Vector()) / len(neighbors)
            vert.co = vert.co.lerp(avg_pos, smooth_factor * 0.1)

def adaptive_decimate(bm, ratio):
    """
    Decimate mesh adaptively based on curvature
    """
    # Calculate face priorities based on curvature
    face_priorities = []
    
    for face in bm.faces:
        # Calculate face curvature as average of vertex curvatures
        avg_curvature = sum(calculate_vertex_curvature_simple(v) for v in face.verts) / len(face.verts)
        face_priorities.append((face, avg_curvature))
    
    # Sort by curvature (lowest first for removal)
    face_priorities.sort(key=lambda x: x[1])
    
    # Remove faces with lowest curvature
    faces_to_remove = int(len(bm.faces) * (1.0 - ratio))
    faces_to_dissolve = [fp[0] for fp in face_priorities[:faces_to_remove]]
    
    # Dissolve faces
    bmesh.ops.dissolve_faces(bm, faces=faces_to_dissolve)

def adaptive_subdivide(bm, target_new_faces):
    """
    Subdivide mesh adaptively in high-curvature areas
    """
    # Calculate edge priorities based on adjacent face curvature
    edge_priorities = []
    
    for edge in bm.edges:
        if len(edge.link_faces) != 2:
            continue
        
        # Calculate curvature of adjacent faces
        curvature = 0.0
        for face in edge.link_faces:
            face_curvature = sum(calculate_vertex_curvature_simple(v) for v in face.verts) / len(face.verts)
            curvature += face_curvature
        
        edge_priorities.append((edge, curvature))
    
    # Sort by curvature (highest first for subdivision)
    edge_priorities.sort(key=lambda x: x[1], reverse=True)
    
    # Subdivide edges with highest curvature
    edges_to_subdivide = min(target_new_faces // 2, len(edge_priorities))
    edges = [ep[0] for ep in edge_priorities[:edges_to_subdivide]]
    
    if edges:
        bmesh.ops.subdivide_edges(bm, edges=edges, cuts=1, use_grid_fill=True)

def calculate_vertex_curvature_simple(vert):
    """
    Simple curvature calculation for a vertex
    """
    if len(vert.link_edges) < 3:
        return 0.0
    
    # Use normal deviation as curvature measure
    normal = vert.normal
    neighbor_normals = []
    
    for edge in vert.link_edges:
        other_vert = edge.other_vert(vert)
        neighbor_normals.append(other_vert.normal)
    
    if not neighbor_normals:
        return 0.0
    
    # Calculate average deviation from vertex normal
    total_deviation = 0.0
    for n_normal in neighbor_normals:
        deviation = 1.0 - normal.dot(n_normal)
        total_deviation += max(0.0, deviation)
    
    return total_deviation / len(neighbor_normals)

def improve_quad_topology(obj):
    """
    Improve quad topology after remeshing
    """
    try:
        # Enter edit mode
        bpy.context.view_layer.objects.active = obj
        bpy.ops.object.mode_set(mode='EDIT')

        # Select all
        bpy.ops.mesh.select_all(action='SELECT')

        # Convert triangles to quads where possible
        bpy.ops.mesh.tris_convert_to_quads(
            face_threshold=0.698132,  # 40 degrees
            shape_threshold=0.698132
        )

        # Clean up mesh
        bpy.ops.mesh.remove_doubles(threshold=0.001)
        bpy.ops.mesh.dissolve_degenerate(threshold=0.001)

        # Return to object mode
        bpy.ops.object.mode_set(mode='OBJECT')

    except Exception as e:
        print(f"Quad topology improvement failed: {e}")

def optimize_voxel_topology(obj, props):
    """
    Optimize topology after voxel remeshing
    """
    try:
        mesh = obj.data
        bm = bmesh.new()
        bm.from_mesh(mesh)

        # Ensure face indices are valid
        bm.faces.ensure_lookup_table()
        bm.edges.ensure_lookup_table()
        bm.verts.ensure_lookup_table()

        # Smooth vertices slightly to improve quality
        for vert in bm.verts:
            if len(vert.link_edges) > 2:
                neighbors = [edge.other_vert(vert).co for edge in vert.link_edges]
                if neighbors:
                    avg_pos = sum(neighbors, Vector()) / len(neighbors)
                    vert.co = vert.co.lerp(avg_pos, 0.1)  # Light smoothing

        # Convert triangles to quads where beneficial
        bmesh.ops.join_triangles(bm, faces=bm.faces,
                                angle_face_threshold=0.698132,  # 40 degrees
                                angle_shape_threshold=0.698132)

        # Update mesh
        bm.to_mesh(mesh)
        bm.free()

    except Exception as e:
        print(f"Voxel topology optimization failed: {e}")

def calculate_flow_directions(bm):
    """
    Calculate principal flow directions for each vertex
    """
    flow_directions = {}

    for vert in bm.verts:
        if len(vert.link_edges) < 2:
            flow_directions[vert.index] = Vector((1, 0, 0))
            continue

        # Calculate principal curvature directions
        neighbors = [edge.other_vert(vert) for edge in vert.link_edges]

        if len(neighbors) < 3:
            flow_directions[vert.index] = Vector((1, 0, 0))
            continue

        # Use covariance matrix to find principal directions
        center = vert.co
        positions = [n.co - center for n in neighbors]

        # Calculate covariance matrix
        cov_matrix = mathutils.Matrix.Identity(3)
        for pos in positions:
            for i in range(3):
                for j in range(3):
                    cov_matrix[i][j] += pos[i] * pos[j]

        # Normalize
        if positions:
            cov_matrix *= 1.0 / len(positions)

        # Find principal direction (largest eigenvalue direction)
        # Simplified: use the direction of maximum variance
        max_var = 0.0
        principal_dir = Vector((1, 0, 0))

        for pos in positions:
            var = pos.length_squared
            if var > max_var:
                max_var = var
                principal_dir = pos.normalized()

        flow_directions[vert.index] = principal_dir

    return flow_directions

def optimize_edge_flow_advanced(bm, curvatures, flow_directions):
    """
    Advanced edge flow optimization using curvature and flow directions
    """
    # Smooth vertices along flow directions
    for vert in bm.verts:
        if len(vert.link_edges) < 2:
            continue

        curvature = curvatures.get(vert.index, 0.0)
        flow_dir = flow_directions.get(vert.index, Vector((1, 0, 0)))

        # Calculate flow-aligned smoothing
        neighbors = [edge.other_vert(vert) for edge in vert.link_edges]
        flow_aligned_pos = Vector()
        weight_sum = 0.0

        for neighbor in neighbors:
            to_neighbor = neighbor.co - vert.co
            # Weight by alignment with flow direction
            alignment = abs(to_neighbor.normalized().dot(flow_dir))
            weight = alignment * alignment  # Square for stronger effect

            flow_aligned_pos += neighbor.co * weight
            weight_sum += weight

        if weight_sum > 0:
            flow_aligned_pos /= weight_sum

            # Blend with current position based on curvature
            # High curvature areas get less smoothing
            smooth_factor = max(0.05, 0.2 * (1.0 - min(1.0, curvature * 5.0)))
            vert.co = vert.co.lerp(flow_aligned_pos, smooth_factor)

def cleanup_edge_flow_topology(bm):
    """
    Clean up topology after edge flow optimization
    """
    # Remove degenerate faces
    bmesh.ops.dissolve_degenerate(bm, edges=bm.edges, dist=0.001)

    # Join triangles to form quads where it improves flow
    bmesh.ops.join_triangles(bm, faces=bm.faces,
                            angle_face_threshold=0.698132,  # 40 degrees
                            angle_shape_threshold=0.523599)  # 30 degrees

    # Remove doubles
    bmesh.ops.remove_doubles(bm, verts=bm.verts, dist=0.001)

def calculate_adaptive_curvature_map(bm):
    """
    Calculate comprehensive curvature map for adaptive retopology
    """
    curvature_map = {}

    for vert in bm.verts:
        # Calculate multiple curvature measures
        gaussian_curvature = calculate_gaussian_curvature(vert)
        mean_curvature = calculate_mean_curvature(vert)
        detail_measure = calculate_detail_measure(vert)

        # Combine measures for overall importance score
        importance = (gaussian_curvature * 0.4 +
                     mean_curvature * 0.4 +
                     detail_measure * 0.2)

        curvature_map[vert.index] = {
            'gaussian': gaussian_curvature,
            'mean': mean_curvature,
            'detail': detail_measure,
            'importance': importance
        }

    return curvature_map

def calculate_gaussian_curvature(vert):
    """
    Calculate Gaussian curvature at vertex
    """
    if len(vert.link_faces) < 3:
        return 0.0

    # Calculate angle defect
    angle_sum = 0.0
    for face in vert.link_faces:
        # Find the angle at this vertex in the face
        face_verts = list(face.verts)
        if vert in face_verts:
            idx = face_verts.index(vert)
            prev_vert = face_verts[idx - 1]
            next_vert = face_verts[(idx + 1) % len(face_verts)]

            # Calculate angle
            v1 = (prev_vert.co - vert.co).normalized()
            v2 = (next_vert.co - vert.co).normalized()
            angle = v1.angle(v2)
            angle_sum += angle

    # Gaussian curvature is 2π - angle sum
    gaussian_curvature = (2 * math.pi - angle_sum) / (math.pi * 2)
    return abs(gaussian_curvature)

def calculate_mean_curvature(vert):
    """
    Calculate mean curvature at vertex
    """
    if len(vert.link_edges) < 3:
        return 0.0

    # Use cotangent Laplacian
    laplacian = Vector()
    weight_sum = 0.0

    for edge in vert.link_edges:
        other_vert = edge.other_vert(vert)

        # Calculate cotangent weights from adjacent faces
        weight = 0.0
        for face in edge.link_faces:
            if len(face.verts) >= 3:
                # Find the third vertex in the triangle
                face_verts = [v for v in face.verts if v != vert and v != other_vert]
                if face_verts:
                    third_vert = face_verts[0]

                    # Calculate cotangent
                    v1 = (vert.co - third_vert.co).normalized()
                    v2 = (other_vert.co - third_vert.co).normalized()
                    cos_angle = v1.dot(v2)
                    sin_angle = v1.cross(v2).length

                    if sin_angle > 0.001:  # Avoid division by zero
                        cot = cos_angle / sin_angle
                        weight += max(0.0, cot)  # Clamp negative weights

        if weight > 0:
            laplacian += (other_vert.co - vert.co) * weight
            weight_sum += weight

    if weight_sum > 0:
        laplacian /= weight_sum
        return laplacian.length

    return 0.0

def calculate_detail_measure(vert):
    """
    Calculate local detail/feature measure
    """
    if len(vert.link_edges) < 2:
        return 0.0

    # Measure local variation in edge lengths and angles
    edge_lengths = [edge.calc_length() for edge in vert.link_edges]

    if not edge_lengths:
        return 0.0

    # Calculate coefficient of variation for edge lengths
    mean_length = sum(edge_lengths) / len(edge_lengths)
    if mean_length > 0:
        variance = sum((l - mean_length) ** 2 for l in edge_lengths) / len(edge_lengths)
        cv = math.sqrt(variance) / mean_length
        return min(1.0, cv)  # Normalize to [0, 1]

    return 0.0

def adaptive_decimate_advanced(bm, ratio, curvature_map):
    """
    Advanced decimation based on curvature importance
    """
    # Calculate face importance scores
    face_scores = []

    for face in bm.faces:
        # Average importance of face vertices
        importance_sum = 0.0
        for vert in face.verts:
            vert_data = curvature_map.get(vert.index, {'importance': 0.0})
            importance_sum += vert_data['importance']

        avg_importance = importance_sum / len(face.verts)
        face_scores.append((face, avg_importance))

    # Sort by importance (lowest first for removal)
    face_scores.sort(key=lambda x: x[1])

    # Remove faces with lowest importance
    faces_to_remove = int(len(bm.faces) * (1.0 - ratio))
    faces_to_dissolve = [fs[0] for fs in face_scores[:faces_to_remove]]

    # Dissolve faces gradually to maintain manifold geometry
    batch_size = max(1, len(faces_to_dissolve) // 10)
    for i in range(0, len(faces_to_dissolve), batch_size):
        batch = faces_to_dissolve[i:i + batch_size]
        valid_faces = [f for f in batch if f.is_valid]
        if valid_faces:
            bmesh.ops.dissolve_faces(bm, faces=valid_faces)

def adaptive_subdivide_advanced(bm, target_new_faces, curvature_map):
    """
    Advanced subdivision based on curvature importance
    """
    # Calculate edge importance scores
    edge_scores = []

    for edge in bm.edges:
        if len(edge.link_faces) != 2:
            continue

        # Average importance of edge vertices
        importance_sum = 0.0
        for vert in edge.verts:
            vert_data = curvature_map.get(vert.index, {'importance': 0.0})
            importance_sum += vert_data['importance']

        avg_importance = importance_sum / 2.0

        # Also consider edge length (longer edges in important areas get priority)
        edge_length = edge.calc_length()
        combined_score = avg_importance * edge_length

        edge_scores.append((edge, combined_score))

    # Sort by combined score (highest first for subdivision)
    edge_scores.sort(key=lambda x: x[1], reverse=True)

    # Subdivide edges with highest scores
    edges_to_subdivide = min(target_new_faces // 2, len(edge_scores))
    edges = [es[0] for es in edge_scores[:edges_to_subdivide]]

    if edges:
        # Subdivide in batches to avoid issues
        batch_size = max(1, len(edges) // 5)
        for i in range(0, len(edges), batch_size):
            batch = edges[i:i + batch_size]
            valid_edges = [e for e in batch if e.is_valid]
            if valid_edges:
                bmesh.ops.subdivide_edges(bm, edges=valid_edges, cuts=1, use_grid_fill=True)

def optimize_adaptive_flow(bm, curvature_map, flow_directions):
    """
    Optimize edge flow in adaptive retopology
    """
    # Smooth vertices based on importance and flow
    for vert in bm.verts:
        if len(vert.link_edges) < 2:
            continue

        vert_data = curvature_map.get(vert.index, {'importance': 0.0})
        importance = vert_data['importance']
        flow_dir = flow_directions.get(vert.index, Vector((1, 0, 0)))

        # High importance areas get less smoothing
        smooth_factor = max(0.02, 0.15 * (1.0 - min(1.0, importance * 2.0)))

        # Calculate flow-aligned smoothing
        neighbors = [edge.other_vert(vert) for edge in vert.link_edges]
        if neighbors:
            flow_aligned_pos = Vector()
            weight_sum = 0.0

            for neighbor in neighbors:
                to_neighbor = neighbor.co - vert.co
                if to_neighbor.length > 0:
                    alignment = abs(to_neighbor.normalized().dot(flow_dir))
                    weight = alignment * alignment

                    flow_aligned_pos += neighbor.co * weight
                    weight_sum += weight

            if weight_sum > 0:
                flow_aligned_pos /= weight_sum
                vert.co = vert.co.lerp(flow_aligned_pos, smooth_factor)

def finalize_adaptive_topology(bm, props):
    """
    Final cleanup and quality improvement for adaptive topology
    """
    # Remove degenerate geometry
    bmesh.ops.dissolve_degenerate(bm, edges=bm.edges, dist=0.001)

    # Join triangles to quads where beneficial
    bmesh.ops.join_triangles(bm, faces=bm.faces,
                            angle_face_threshold=0.698132,  # 40 degrees
                            angle_shape_threshold=0.523599)  # 30 degrees

    # Remove doubles
    bmesh.ops.remove_doubles(bm, verts=bm.verts, dist=0.001)

    # Smooth shading preparation
    if props.smooth_shading:
        for face in bm.faces:
            face.smooth = True
