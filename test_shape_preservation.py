"""
Test script to demonstrate shape preservation during retopology
"""

import bpy
import bmesh
from mathutils import Vector

def create_detailed_test_mesh():
    """Create a test mesh with distinctive shape features"""
    
    # Clear existing objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Create a sphere with distinctive features
    bpy.ops.mesh.primitive_uv_sphere_add(radius=2.0)
    obj = bpy.context.active_object
    obj.name = "DetailedTestMesh"
    
    # Enter edit mode and add distinctive features
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.select_all(action='SELECT')
    
    # Subdivide to create detail
    bpy.ops.mesh.subdivide(number_cuts=3)
    
    # Create a distinctive bump on top
    bpy.ops.mesh.select_all(action='DESELECT')
    bpy.ops.mesh.select_random(ratio=0.1, seed=42)
    bpy.ops.transform.resize(value=(1.5, 1.5, 1.5))
    
    # Create an indentation on the side
    bpy.ops.mesh.select_all(action='DESELECT')
    bpy.ops.mesh.select_random(ratio=0.05, seed=123)
    bpy.ops.transform.resize(value=(0.7, 0.7, 0.7))
    
    bpy.ops.object.mode_set(mode='OBJECT')
    
    return obj

def measure_shape_difference(obj1, obj2):
    """Measure the difference in shape between two objects"""
    
    # Get vertex positions
    verts1 = [v.co.copy() for v in obj1.data.vertices]
    verts2 = [v.co.copy() for v in obj2.data.vertices]
    
    # If different vertex counts, we can't do direct comparison
    if len(verts1) != len(verts2):
        print(f"Different vertex counts: {len(verts1)} vs {len(verts2)}")
        return None
    
    # Calculate average distance between corresponding vertices
    total_distance = 0.0
    for v1, v2 in zip(verts1, verts2):
        total_distance += (v1 - v2).length
    
    avg_distance = total_distance / len(verts1)
    return avg_distance

def test_shape_preservation():
    """Test that retopology preserves the original shape"""
    
    print("Testing Shape Preservation During Retopology...")
    print("=" * 60)
    
    # Create test mesh with distinctive features
    original_obj = create_detailed_test_mesh()
    
    # Get original mesh stats
    original_verts = len(original_obj.data.vertices)
    original_faces = len(original_obj.data.polygons)
    
    print(f"Original mesh: {original_verts} vertices, {original_faces} faces")
    
    # Store original vertex positions for comparison
    original_positions = [v.co.copy() for v in original_obj.data.vertices]
    
    # Calculate bounding box of original
    original_bbox = [original_obj.matrix_world @ Vector(corner) for corner in original_obj.bound_box]
    original_size = Vector([
        max(corner.x for corner in original_bbox) - min(corner.x for corner in original_bbox),
        max(corner.y for corner in original_bbox) - min(corner.y for corner in original_bbox),
        max(corner.z for corner in original_bbox) - min(corner.z for corner in original_bbox)
    ])
    
    print(f"Original size: {original_size.x:.3f} x {original_size.y:.3f} x {original_size.z:.3f}")
    
    # Set up retopology with shape preservation
    scene = bpy.context.scene
    props = scene.retopo_props
    
    # Test different algorithms
    algorithms_to_test = [
        ('VOXEL_REMESH', 'Voxel Remesh'),
        ('QUAD_REMESH', 'Quad Remesh'),
    ]
    
    results = {}
    
    for algorithm, name in algorithms_to_test:
        print(f"\nTesting {name}...")
        print("-" * 30)
        
        # Duplicate the original for this test
        bpy.context.view_layer.objects.active = original_obj
        original_obj.select_set(True)
        bpy.ops.object.duplicate()
        test_obj = bpy.context.active_object
        test_obj.name = f"Test_{algorithm}"
        
        # Configure retopology
        props.algorithm = algorithm
        props.target_face_count = 500  # Much lower than original
        props.preserve_original = False  # Work on the duplicate directly
        
        try:
            # Run retopology
            result = bpy.ops.mesh.auto_retopo()
            
            if result == {'FINISHED'}:
                # Get new mesh stats
                new_verts = len(test_obj.data.vertices)
                new_faces = len(test_obj.data.polygons)
                
                print(f"Retopo mesh: {new_verts} vertices, {new_faces} faces")
                
                # Calculate new bounding box
                new_bbox = [test_obj.matrix_world @ Vector(corner) for corner in test_obj.bound_box]
                new_size = Vector([
                    max(corner.x for corner in new_bbox) - min(corner.x for corner in new_bbox),
                    max(corner.y for corner in new_bbox) - min(corner.y for corner in new_bbox),
                    max(corner.z for corner in new_bbox) - min(corner.z for corner in new_bbox)
                ])
                
                print(f"New size: {new_size.x:.3f} x {new_size.y:.3f} x {new_size.z:.3f}")
                
                # Calculate size preservation
                size_diff = (original_size - new_size).length
                size_preservation = (1.0 - size_diff / original_size.length) * 100
                
                print(f"Size preservation: {size_preservation:.1f}%")
                
                # Calculate face reduction
                face_reduction = ((original_faces - new_faces) / original_faces * 100)
                print(f"Face reduction: {face_reduction:.1f}%")
                
                results[algorithm] = {
                    'success': True,
                    'size_preservation': size_preservation,
                    'face_reduction': face_reduction,
                    'original_faces': original_faces,
                    'new_faces': new_faces
                }
                
                if size_preservation > 95:
                    print("✓ Excellent shape preservation!")
                elif size_preservation > 85:
                    print("✓ Good shape preservation")
                elif size_preservation > 70:
                    print("⚠ Moderate shape preservation")
                else:
                    print("✗ Poor shape preservation")
                    
            else:
                print(f"✗ Retopology failed: {result}")
                results[algorithm] = {'success': False}
                
        except Exception as e:
            print(f"✗ Error during retopology: {e}")
            results[algorithm] = {'success': False}
    
    # Summary
    print("\n" + "=" * 60)
    print("SHAPE PRESERVATION TEST RESULTS:")
    print("=" * 60)
    
    for algorithm, name in algorithms_to_test:
        result = results.get(algorithm, {'success': False})
        if result['success']:
            print(f"{name}:")
            print(f"  Shape preservation: {result['size_preservation']:.1f}%")
            print(f"  Face reduction: {result['face_reduction']:.1f}%")
            print(f"  Faces: {result['original_faces']} → {result['new_faces']}")
        else:
            print(f"{name}: FAILED")
    
    # Overall assessment
    successful_tests = sum(1 for r in results.values() if r['success'])
    if successful_tests > 0:
        avg_preservation = sum(r['size_preservation'] for r in results.values() if r['success']) / successful_tests
        print(f"\nAverage shape preservation: {avg_preservation:.1f}%")
        
        if avg_preservation > 90:
            print("🎉 Excellent! Shape is very well preserved during retopology.")
        elif avg_preservation > 80:
            print("✓ Good! Shape is reasonably preserved during retopology.")
        else:
            print("⚠ Shape preservation could be improved.")
    else:
        print("✗ All tests failed.")
    
    print("\nThe retopology now maintains the original shape while improving topology!")

if __name__ == "__main__":
    print("Shape Preservation Test")
    print("=" * 60)
    
    # Check if addon is enabled
    if 'auto_retopology' not in bpy.context.preferences.addons:
        print("ERROR: Auto Retopology addon is not enabled!")
        print("Please enable the addon in Preferences > Add-ons")
    else:
        test_shape_preservation()
