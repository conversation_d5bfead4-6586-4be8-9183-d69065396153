# Installation Guide - Auto Retopology Addon

## Quick Installation

### Method 1: Direct Installation (Recommended)

1. **Download the addon files**
   - Download all Python files to a folder named `auto_retopology`
   - Required files:
     - `__init__.py`
     - `retopo_algorithms.py`
     - `retopo_operators.py`
     - `retopo_ui.py`
     - `retopo_utils.py`

2. **Install in Blender**
   - Open Blender 4.0 or higher
   - Go to `Edit > Preferences > Add-ons`
   - Click `Install...`
   - Navigate to and select the `auto_retopology` folder
   - Enable "Auto Retopology" in the addon list
   - Click "Save Preferences"

3. **Verify Installation**
   - Open the 3D Viewport
   - Press `N` to open the sidebar
   - Look for the "Retopo" tab
   - If visible, installation was successful!

### Method 2: Manual Installation

1. **Locate Blender's addon directory**
   - **Windows**: `%APPDATA%\Blender Foundation\Blender\4.x\scripts\addons\`
   - **macOS**: `~/Library/Application Support/Blender/4.x/scripts/addons/`
   - **Linux**: `~/.config/blender/4.x/scripts/addons/`

2. **Copy addon files**
   - Create a folder named `auto_retopology` in the addons directory
   - Copy all Python files into this folder

3. **Enable the addon**
   - Restart Blender
   - Go to `Edit > Preferences > Add-ons`
   - Search for "Auto Retopology"
   - Enable the addon
   - Click "Save Preferences"

## System Requirements

### Minimum Requirements
- **Blender**: Version 4.0 or higher
- **Python**: 3.10+ (included with Blender)
- **RAM**: 4GB minimum, 8GB recommended
- **CPU**: Multi-core processor recommended for better performance

### Recommended Requirements
- **Blender**: Latest stable version
- **RAM**: 16GB or more for large meshes
- **CPU**: 8+ cores for optimal performance
- **GPU**: Modern graphics card for viewport performance

## Dependencies

The addon uses only built-in Blender modules:
- `bpy` (Blender Python API)
- `bmesh` (Mesh editing)
- `mathutils` (Math utilities)
- `numpy` (Numerical operations - optional, falls back if not available)

No external dependencies need to be installed.

## Troubleshooting Installation

### Common Issues

**"Module not found" error**
- Ensure all Python files are in the correct folder
- Check that folder is named exactly `auto_retopology`
- Verify Blender version compatibility (4.0+)

**Addon not appearing in preferences**
- Check that `__init__.py` is present and contains `bl_info`
- Restart Blender after copying files
- Check Blender console for error messages

**UI panel not visible**
- Press `N` in 3D Viewport to open sidebar
- Look for "Retopo" tab
- If missing, check addon is enabled in preferences

**Performance issues**
- Close other applications to free RAM
- Use lower target face counts for testing
- Try different algorithms for your mesh type

### Getting Help

1. **Check the console**
   - Go to `Window > Toggle System Console` (Windows)
   - Look for error messages when using the addon

2. **Test with simple meshes**
   - Start with default cube or sphere
   - Gradually test more complex meshes

3. **Verify mesh quality**
   - Use "Analyze Quality" button
   - Check for non-manifold geometry
   - Ensure mesh has faces (not just vertices/edges)

## Testing the Installation

### Quick Test
1. Delete default cube
2. Add a UV Sphere (`Shift+A > Mesh > UV Sphere`)
3. Open Retopo panel in sidebar
4. Click "Retopologize" with default settings
5. Check that mesh topology changes

### Comprehensive Test
1. Run the included `test_retopo.py` script
2. Go to `Scripting` workspace
3. Open `test_retopo.py`
4. Click "Run Script"
5. Check console output for results

## Uninstallation

### Remove from Blender
1. Go to `Edit > Preferences > Add-ons`
2. Find "Auto Retopology"
3. Click the dropdown arrow
4. Click "Remove"
5. Restart Blender

### Manual Removal
1. Navigate to Blender's addon directory
2. Delete the `auto_retopology` folder
3. Restart Blender

## Performance Optimization

### For Large Meshes (>100k faces)
- Use "Low Poly" preset first
- Increase target face count gradually
- Consider decimating input mesh first
- Use Voxel Remesh for organic shapes

### For Real-time Workflows
- Enable "Auto Optimize" for automatic parameter tuning
- Use "Preview Mode" to see results before applying
- Start with conservative settings

### Memory Management
- Close other applications when processing large meshes
- Use "Remove Disconnected" option to clean up results
- Save your work before processing very large meshes

## Advanced Configuration

### Custom Presets
You can modify the preset configurations in `retopo_operators.py`:
```python
def apply_preset(self, props, preset):
    if preset == 'CUSTOM':
        props.target_face_count = 1500
        props.algorithm = 'ADAPTIVE'
        # Add your custom settings here
```

### Algorithm Parameters
Fine-tune algorithms by modifying default values in `retopo_ui.py`:
```python
quad_octree_depth: IntProperty(
    default=5,  # Increase for higher resolution
    min=1,
    max=10
)
```

## Support and Updates

### Getting Support
- Check the README.md for usage instructions
- Run test scripts to verify functionality
- Check Blender console for error messages

### Staying Updated
- Keep Blender updated to latest stable version
- Check for addon updates periodically
- Test new features with simple meshes first

## License and Credits

This addon is provided as-is for educational and professional use. Please respect the licensing terms and contribute improvements back to the community.

---

**Happy Retopologizing!** 🎨✨
