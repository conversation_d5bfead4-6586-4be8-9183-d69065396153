"""
User Interface for Auto Retopology Addon
"""

import bpy
from bpy.types import Panel, PropertyGroup
from bpy.props import (
    FloatProperty,
    IntProperty,
    BoolProperty,
    EnumProperty,
)

class RetopoProperties(PropertyGroup):
    """Properties for retopology settings"""
    
    # Algorithm selection
    algorithm: EnumProperty(
        name="Algorithm",
        description="Choose retopology algorithm",
        items=[
            ('QUAD_REMESH', "Quad Remesh", "<PERSON><PERSON><PERSON>'s built-in quad remesher"),
            ('VOXEL_REMESH', "Voxel Remesh", "Voxel-based remeshing for organic shapes"),
            ('EDGE_FLOW', "Edge Flow", "Edge-flow aware retopology"),
            ('ADAPTIVE', "Adaptive", "Adaptive density based on curvature"),
        ],
        default='QUAD_REMESH'
    )
    
    # General settings
    target_face_count: IntProperty(
        name="Target Face Count",
        description="Approximate number of faces in output mesh. Higher values may require more processing time and memory",
        default=1000,
        min=100,
        max=10000000  # 10 million faces - practically unlimited
    )
    
    preserve_sharp_edges: <PERSON>ol<PERSON>roperty(
        name="Preserve Sharp Edges",
        description="Try to preserve sharp edges and corners",
        default=True
    )
    
    smooth_shading: BoolProperty(
        name="Smooth Shading",
        description="Apply smooth shading to output mesh",
        default=True
    )
    
    # Quad Remesh specific
    quad_octree_depth: IntProperty(
        name="Octree Depth",
        description="Resolution of quad remesh",
        default=4,
        min=1,
        max=10
    )
    
    quad_scale: FloatProperty(
        name="Scale",
        description="Scale factor for quad remesh",
        default=0.99,
        min=0.1,
        max=2.0
    )
    
    # Voxel Remesh specific
    voxel_size: FloatProperty(
        name="Voxel Size",
        description="Size of voxels for remeshing",
        default=0.1,
        min=0.001,
        max=1.0
    )
    
    voxel_adaptivity: FloatProperty(
        name="Adaptivity",
        description="Reduce face count in flat areas",
        default=0.0,
        min=0.0,
        max=1.0
    )
    
    # Edge Flow specific
    edge_flow_iterations: IntProperty(
        name="Iterations",
        description="Number of edge flow iterations",
        default=3,
        min=1,
        max=10
    )
    
    # Quality settings
    min_edge_length: FloatProperty(
        name="Min Edge Length",
        description="Minimum edge length ratio",
        default=0.1,
        min=0.01,
        max=1.0
    )

    max_edge_length: FloatProperty(
        name="Max Edge Length",
        description="Maximum edge length ratio",
        default=2.0,
        min=1.0,
        max=10.0
    )

    # Advanced settings
    auto_optimize: BoolProperty(
        name="Auto Optimize",
        description="Automatically optimize parameters based on mesh analysis",
        default=True
    )

    preview_mode: BoolProperty(
        name="Preview Mode",
        description="Show wireframe preview of retopology result",
        default=False
    )

    preserve_boundaries: BoolProperty(
        name="Preserve Boundaries",
        description="Try to preserve mesh boundaries and holes",
        default=True
    )

    # Quality thresholds
    min_quad_ratio: FloatProperty(
        name="Min Quad Ratio",
        description="Minimum acceptable quad ratio",
        default=0.7,
        min=0.0,
        max=1.0
    )

    max_triangle_ratio: FloatProperty(
        name="Max Triangle Ratio",
        description="Maximum acceptable triangle ratio",
        default=0.3,
        min=0.0,
        max=1.0
    )

class RETOPO_PT_main_panel(Panel):
    """Main retopology panel"""
    bl_label = "Auto Retopology"
    bl_idname = "RETOPO_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Retopo"
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        props = scene.retopo_props
        
        # Algorithm selection
        layout.prop(props, "algorithm")
        
        # Target face count
        layout.prop(props, "target_face_count")
        
        # Main retopology button
        layout.separator()
        row = layout.row(align=True)
        row.scale_y = 2.0
        main_op = row.operator("mesh.auto_retopo", text="Retopologize", icon='MOD_REMESH')
        main_op.preset = ""  # Explicitly clear preset to use manual settings
        
        # Quick presets
        layout.separator()
        layout.label(text="Quick Presets:")
        row = layout.row(align=True)
        row.operator("mesh.auto_retopo", text="Low Poly").preset = 'LOW_POLY'
        row.operator("mesh.auto_retopo", text="High Detail").preset = 'HIGH_DETAIL'

        row = layout.row(align=True)
        row.operator("mesh.auto_retopo", text="Game Asset").preset = 'GAME_ASSET'
        row.operator("mesh.auto_retopo", text="Animation").preset = 'ANIMATION'

        # Advanced options
        layout.separator()
        layout.prop(props, "auto_optimize")
        layout.prop(props, "preview_mode")

        # Mesh info
        obj = context.active_object
        if obj and obj.type == 'MESH':
            layout.separator()
            layout.label(text="Current Mesh:")
            col = layout.column(align=True)
            col.label(text=f"Vertices: {len(obj.data.vertices):,}")
            col.label(text=f"Faces: {len(obj.data.polygons):,}")

            # Estimated result
            if props.target_face_count > 0:
                reduction = (1.0 - props.target_face_count / max(1, len(obj.data.polygons))) * 100
                if reduction > 0:
                    col.label(text=f"Reduction: {reduction:.1f}%")
                else:
                    col.label(text=f"Increase: {-reduction:.1f}%")

                # Performance warning for very high face counts
                if props.target_face_count > 100000:
                    col.separator()
                    warning_col = col.column(align=True)
                    warning_col.alert = True
                    if props.target_face_count > 1000000:
                        warning_col.label(text="⚠ Very High Face Count", icon='ERROR')
                        warning_col.label(text="May require significant time/memory")
                    else:
                        warning_col.label(text="⚠ High Face Count", icon='INFO')
                        warning_col.label(text="Processing may take longer")

class RETOPO_PT_settings_panel(Panel):
    """Settings panel for retopology"""
    bl_label = "Settings"
    bl_idname = "RETOPO_PT_settings_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Retopo"
    bl_parent_id = "RETOPO_PT_main_panel"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        props = scene.retopo_props
        
        # General settings
        layout.prop(props, "preserve_sharp_edges")
        layout.prop(props, "preserve_boundaries")
        layout.prop(props, "smooth_shading")

        layout.separator()

        # Algorithm-specific settings
        if props.algorithm == 'QUAD_REMESH':
            layout.label(text="Quad Remesh Settings:")
            layout.prop(props, "quad_octree_depth")
            layout.prop(props, "quad_scale")

            if not props.auto_optimize:
                layout.label(text="Manual Tuning:")
                layout.prop(props, "quad_octree_depth", text="Octree Depth")

        elif props.algorithm == 'VOXEL_REMESH':
            layout.label(text="Voxel Remesh Settings:")
            layout.prop(props, "voxel_size")
            layout.prop(props, "voxel_adaptivity")

            if not props.auto_optimize:
                layout.label(text="Manual Tuning:")
                layout.prop(props, "voxel_size", text="Voxel Size")

        elif props.algorithm == 'EDGE_FLOW':
            layout.label(text="Edge Flow Settings:")
            layout.prop(props, "edge_flow_iterations")

        elif props.algorithm == 'ADAPTIVE':
            layout.label(text="Adaptive Settings:")
            layout.prop(props, "voxel_adaptivity", text="Adaptivity")
            layout.prop(props, "edge_flow_iterations", text="Flow Iterations")

        # Quality settings
        layout.separator()
        layout.label(text="Quality Control:")
        layout.prop(props, "min_edge_length")
        layout.prop(props, "max_edge_length")

        layout.separator()
        layout.label(text="Quality Thresholds:")
        layout.prop(props, "min_quad_ratio")
        layout.prop(props, "max_triangle_ratio")

class RETOPO_PT_quality_panel(Panel):
    """Quality analysis panel"""
    bl_label = "Mesh Quality"
    bl_idname = "RETOPO_PT_quality_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Retopo"
    bl_parent_id = "RETOPO_PT_main_panel"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        props = scene.retopo_props

        # Quality analysis button
        row = layout.row(align=True)
        row.operator("mesh.analyze_mesh_quality", text="Analyze Quality", icon='VIEWZOOM')
        row.operator("mesh.compare_meshes", text="Compare", icon='UV_SYNC_SELECT')

        # Display quality metrics if available
        obj = context.active_object
        if obj and obj.type == 'MESH' and hasattr(obj, 'retopo_quality'):
            quality = obj.retopo_quality

            layout.separator()
            layout.label(text="Quality Metrics:")

            # Create quality visualization
            col = layout.column(align=True)

            # Quad ratio with color coding
            quad_ratio = quality.get('quad_ratio', 0)
            if quad_ratio >= props.min_quad_ratio:
                col.label(text=f"✓ Quad Ratio: {quad_ratio:.1%}", icon='CHECKMARK')
            else:
                col.label(text=f"⚠ Quad Ratio: {quad_ratio:.1%}", icon='ERROR')

            # Triangle ratio
            triangle_count = quality.get('triangle_count', 0)
            total_faces = quality.get('total_faces', 1)
            triangle_ratio = triangle_count / total_faces if total_faces > 0 else 0

            if triangle_ratio <= props.max_triangle_ratio:
                col.label(text=f"✓ Triangles: {triangle_count} ({triangle_ratio:.1%})")
            else:
                col.label(text=f"⚠ Triangles: {triangle_count} ({triangle_ratio:.1%})")

            # Other metrics
            col.label(text=f"N-gons: {quality.get('ngon_count', 0)}")
            col.label(text=f"Edge Flow: {quality.get('edge_flow_score', 0):.2f}")
            col.label(text=f"Aspect Ratio: {quality.get('aspect_ratio_score', 0):.2f}")

            # Overall quality score
            overall = quality.get('overall_quality', 0)
            layout.separator()
            if overall >= 0.8:
                layout.label(text=f"✓ Overall Quality: {overall:.1%}", icon='CHECKMARK')
            elif overall >= 0.6:
                layout.label(text=f"⚠ Overall Quality: {overall:.1%}", icon='INFO')
            else:
                layout.label(text=f"✗ Overall Quality: {overall:.1%}", icon='ERROR')

            # Recommendations
            if quad_ratio < props.min_quad_ratio:
                layout.separator()
                layout.label(text="Recommendations:", icon='LIGHT_SUN')
                col = layout.column(align=True)
                col.label(text="• Try Quad Remesh algorithm")
                col.label(text="• Increase octree depth")
                col.label(text="• Enable triangle to quad conversion")

        else:
            layout.separator()
            layout.label(text="No quality data available")
            layout.label(text="Run analysis first", icon='INFO')
