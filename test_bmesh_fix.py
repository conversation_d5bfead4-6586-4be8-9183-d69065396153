"""
Test script to verify BMesh memory management fix
"""

import bpy
import bmesh
from . import retopo_utils

def test_analyze_mesh_quality():
    """Test that analyze_mesh_quality doesn't crash with BMesh errors"""
    
    # Create a simple test mesh
    bpy.ops.mesh.primitive_cube_add()
    obj = bpy.context.active_object
    
    try:
        # This should not raise a "BMesh data has been removed" error
        quality_metrics = retopo_utils.analyze_mesh_quality(obj)
        
        print("✓ analyze_mesh_quality completed successfully")
        print(f"  Quad ratio: {quality_metrics.get('quad_ratio', 0):.2%}")
        print(f"  Total faces: {quality_metrics.get('total_faces', 0)}")
        print(f"  Overall quality: {quality_metrics.get('overall_quality', 0):.2f}")
        print(f"  Quality grade: {quality_metrics.get('quality_grade', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"✗ analyze_mesh_quality failed: {e}")
        return False
    
    finally:
        # Clean up
        bpy.ops.object.delete()

def test_multiple_calls():
    """Test multiple consecutive calls to ensure no memory leaks"""
    
    # Create a test mesh
    bpy.ops.mesh.primitive_uv_sphere_add()
    obj = bpy.context.active_object
    
    try:
        success_count = 0
        for i in range(5):
            try:
                quality_metrics = retopo_utils.analyze_mesh_quality(obj)
                success_count += 1
                print(f"  Call {i+1}: ✓ (Quality: {quality_metrics.get('overall_quality', 0):.2f})")
            except Exception as e:
                print(f"  Call {i+1}: ✗ {e}")
        
        print(f"✓ Multiple calls test: {success_count}/5 successful")
        return success_count == 5
        
    finally:
        # Clean up
        bpy.ops.object.delete()

if __name__ == "__main__":
    print("Testing BMesh memory management fix...")
    print("=" * 50)
    
    test1_passed = test_analyze_mesh_quality()
    test2_passed = test_multiple_calls()
    
    print("=" * 50)
    if test1_passed and test2_passed:
        print("✓ All tests passed! BMesh fix is working correctly.")
    else:
        print("✗ Some tests failed. Please check the implementation.")
