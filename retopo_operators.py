"""
Operators for Auto Retopology Addon
"""

import bpy
import bmesh
import mathutils
from bpy.types import Operator
from bpy.props import StringProperty
from . import retopo_algorithms
from . import retopo_utils

class MESH_OT_auto_retopo(Operator):
    """Automatically retopologize the selected mesh"""
    bl_idname = "mesh.auto_retopo"
    bl_label = "Auto Retopology"
    bl_description = "Automatically retopologize the selected mesh using the chosen algorithm"
    bl_options = {'REGISTER', 'UNDO'}
    
    preset: StringProperty(
        name="Preset",
        description="Preset configuration",
        default=""
    )
    
    @classmethod
    def poll(cls, context):
        """Check if operator can be executed"""
        return (context.active_object is not None and 
                context.active_object.type == 'MESH' and
                context.mode == 'OBJECT')
    
    def execute(self, context):
        """Execute the retopology operation"""
        obj = context.active_object
        scene = context.scene
        props = scene.retopo_props

        # Apply preset if specified (only for preset buttons, not main button)
        if self.preset and self.preset != "":
            print(f"Applying preset: {self.preset}")
            self.apply_preset(props, self.preset)
        else:
            print("Using manual settings (no preset)")

        # Validate input
        if not obj or obj.type != 'MESH':
            self.report({'ERROR'}, "Please select a mesh object")
            return {'CANCELLED'}

        if len(obj.data.vertices) == 0:
            self.report({'ERROR'}, "Selected mesh has no vertices")
            return {'CANCELLED'}

        try:
            # Check if we should preserve the original
            if props.preserve_original:
                # Create a duplicate for retopology (non-destructive workflow)
                original_obj = obj

                # Duplicate the object
                bpy.context.view_layer.objects.active = original_obj
                original_obj.select_set(True)
                bpy.ops.object.duplicate()
                retopo_obj = bpy.context.active_object

                # Rename objects for clarity
                if not original_obj.name.endswith("_Original"):
                    original_obj.name = original_obj.name + "_Original"

                retopo_obj.name = original_obj.name.replace("_Original", "_Retopo")

                # Move retopo object slightly to the side so both are visible
                retopo_obj.location.x += retopo_obj.dimensions.x * 1.2

                print(f"Created retopo duplicate: {retopo_obj.name}")

                # Work on the duplicate, not the original
                obj = retopo_obj
            else:
                # Destructive workflow - work directly on the original
                original_obj = None
                retopo_obj = obj
                print("Working directly on original mesh (destructive mode)")
            
            # Perform retopology based on selected algorithm
            if props.algorithm == 'QUAD_REMESH':
                success = retopo_algorithms.quad_remesh(obj, props)
            elif props.algorithm == 'VOXEL_REMESH':
                success = retopo_algorithms.voxel_remesh(obj, props)
            elif props.algorithm == 'EDGE_FLOW':
                success = retopo_algorithms.edge_flow_retopo(obj, props)
            elif props.algorithm == 'ADAPTIVE':
                success = retopo_algorithms.adaptive_retopo(obj, props)
            else:
                self.report({'ERROR'}, f"Unknown algorithm: {props.algorithm}")
                return {'CANCELLED'}
            
            if not success:
                # Restore original mesh if retopology failed
                obj.data = original_mesh
                self.report({'ERROR'}, "Retopology failed")
                return {'CANCELLED'}
            
            # Apply post-processing
            self.post_process(obj, props)
            
            # Update viewport
            context.view_layer.update()
            
            # Get face count info for the report
            final_faces = len(obj.data.polygons)

            if props.preserve_original and original_obj:
                # Non-destructive mode
                original_faces = len(original_obj.data.polygons)
                reduction = ((original_faces - final_faces) / original_faces * 100) if original_faces > 0 else 0

                self.report({'INFO'},
                           f"Retopology completed! Created '{retopo_obj.name}' with {final_faces} faces "
                           f"({reduction:.1f}% reduction) using {props.algorithm}. Original preserved as '{original_obj.name}'.")
            else:
                # Destructive mode
                self.report({'INFO'},
                           f"Retopology completed! Mesh now has {final_faces} faces using {props.algorithm}.")

            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Retopology failed: {str(e)}")
            return {'CANCELLED'}
    
    def apply_preset(self, props, preset):
        """Apply preset configuration"""
        if preset == 'LOW_POLY':
            props.target_face_count = 500
            props.algorithm = 'QUAD_REMESH'
            props.quad_octree_depth = 3
            props.preserve_sharp_edges = True

        elif preset == 'HIGH_DETAIL':
            props.target_face_count = 20000  # Increased from 5000 for truly high detail
            props.algorithm = 'ADAPTIVE'
            props.preserve_sharp_edges = True
            props.voxel_adaptivity = 0.3

        elif preset == 'GAME_ASSET':
            props.target_face_count = 1000
            props.algorithm = 'QUAD_REMESH'
            props.quad_octree_depth = 4
            props.preserve_sharp_edges = True
            props.smooth_shading = False

        elif preset == 'ANIMATION':
            props.target_face_count = 3000
            props.algorithm = 'EDGE_FLOW'
            props.edge_flow_iterations = 4
            props.preserve_sharp_edges = False
            props.smooth_shading = True
    
    def post_process(self, obj, props):
        """Apply post-processing to the retopologized mesh"""
        # Enter edit mode to clean up mesh
        bpy.context.view_layer.objects.active = obj
        bpy.ops.object.mode_set(mode='EDIT')
        
        # Select all
        bpy.ops.mesh.select_all(action='SELECT')
        
        # Remove doubles
        bpy.ops.mesh.remove_doubles(threshold=0.001)
        
        # Recalculate normals
        bpy.ops.mesh.normals_make_consistent(inside=False)
        
        # Apply smooth shading if requested
        if props.smooth_shading:
            bpy.ops.mesh.faces_shade_smooth()
        else:
            bpy.ops.mesh.faces_shade_flat()
        
        # Return to object mode
        bpy.ops.object.mode_set(mode='OBJECT')

class MESH_OT_analyze_mesh_quality(Operator):
    """Analyze mesh quality and topology"""
    bl_idname = "mesh.analyze_mesh_quality"
    bl_label = "Analyze Mesh Quality"
    bl_description = "Analyze the quality and topology of the selected mesh"
    bl_options = {'REGISTER'}
    
    @classmethod
    def poll(cls, context):
        """Check if operator can be executed"""
        return (context.active_object is not None and 
                context.active_object.type == 'MESH')
    
    def execute(self, context):
        """Execute the quality analysis"""
        obj = context.active_object
        
        if not obj or obj.type != 'MESH':
            self.report({'ERROR'}, "Please select a mesh object")
            return {'CANCELLED'}
        
        try:
            # Simple quality analysis without BMesh (temporary fix)
            mesh = obj.data

            # Count face types directly from mesh data
            triangle_count = 0
            quad_count = 0
            ngon_count = 0

            for poly in mesh.polygons:
                if len(poly.vertices) == 3:
                    triangle_count += 1
                elif len(poly.vertices) == 4:
                    quad_count += 1
                else:
                    ngon_count += 1

            total_faces = len(mesh.polygons)
            quad_ratio = quad_count / total_faces if total_faces > 0 else 0.0

            # Store basic results (using custom properties instead of attributes)
            quality_metrics = {
                'quad_ratio': quad_ratio,
                'triangle_count': triangle_count,
                'quad_count': quad_count,
                'ngon_count': ngon_count,
                'total_faces': total_faces,
                'vertex_count': len(mesh.vertices)
            }

            # Store in custom properties (Blender's safe way to store data on objects)
            obj["retopo_quad_ratio"] = quad_ratio
            obj["retopo_triangle_count"] = triangle_count
            obj["retopo_quad_count"] = quad_count
            obj["retopo_ngon_count"] = ngon_count
            obj["retopo_total_faces"] = total_faces
            obj["retopo_vertex_count"] = len(mesh.vertices)

            # Report results
            self.report({'INFO'},
                       f"Quality Analysis: {quad_ratio:.1%} quads, "
                       f"{triangle_count} triangles, {ngon_count} n-gons, "
                       f"{len(mesh.vertices)} vertices, {total_faces} faces")

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Quality analysis failed: {str(e)}")
            return {'CANCELLED'}

class MESH_OT_compare_meshes(Operator):
    """Compare original and retopologized meshes"""
    bl_idname = "mesh.compare_meshes"
    bl_label = "Compare Meshes"
    bl_description = "Compare quality metrics between original and retopologized meshes"
    bl_options = {'REGISTER'}

    @classmethod
    def poll(cls, context):
        """Check if operator can be executed"""
        return (context.active_object is not None and
                context.active_object.type == 'MESH')

    def execute(self, context):
        """Execute the mesh comparison"""
        obj = context.active_object

        if not obj or obj.type != 'MESH':
            self.report({'ERROR'}, "Please select a mesh object")
            return {'CANCELLED'}

        try:
            # Find original mesh (look for backup or similar named object)
            original_obj = None
            obj_name = obj.name

            # Look for common backup naming patterns
            backup_names = [
                f"{obj_name}_original",
                f"{obj_name}_backup",
                f"{obj_name}.001",
                obj_name.replace("_retopo", ""),
                obj_name.replace("_low", "_high"),
            ]

            for backup_name in backup_names:
                if backup_name in bpy.data.objects:
                    candidate = bpy.data.objects[backup_name]
                    if candidate.type == 'MESH' and candidate != obj:
                        original_obj = candidate
                        break

            if not original_obj:
                self.report({'WARNING'}, "No original mesh found for comparison")
                return {'CANCELLED'}

            # Simple analysis without BMesh (temporary fix)
            def simple_mesh_analysis(mesh_obj):
                mesh = mesh_obj.data
                triangle_count = sum(1 for poly in mesh.polygons if len(poly.vertices) == 3)
                quad_count = sum(1 for poly in mesh.polygons if len(poly.vertices) == 4)
                total_faces = len(mesh.polygons)
                quad_ratio = quad_count / total_faces if total_faces > 0 else 0.0
                return {
                    'total_faces': total_faces,
                    'quad_ratio': quad_ratio,
                    'triangle_count': triangle_count,
                    'quad_count': quad_count
                }

            current_quality = simple_mesh_analysis(obj)
            original_quality = simple_mesh_analysis(original_obj)

            # Calculate improvements
            face_reduction = (1.0 - current_quality['total_faces'] / max(1, original_quality['total_faces'])) * 100
            quad_improvement = current_quality['quad_ratio'] - original_quality['quad_ratio']

            # Store comparison results in custom properties
            obj["retopo_original_faces"] = original_quality['total_faces']
            obj["retopo_current_faces"] = current_quality['total_faces']
            obj["retopo_face_reduction"] = face_reduction
            obj["retopo_quad_improvement"] = quad_improvement
            obj["retopo_original_quad_ratio"] = original_quality['quad_ratio']
            obj["retopo_current_quad_ratio"] = current_quality['quad_ratio']

            # Report results
            self.report({'INFO'},
                       f"Comparison: {face_reduction:.1f}% face reduction, "
                       f"Quad ratio: {original_quality['quad_ratio']:.1%} → {current_quality['quad_ratio']:.1%}")

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Mesh comparison failed: {str(e)}")
            return {'CANCELLED'}
