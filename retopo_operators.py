"""
Operators for Auto Retopology Addon
"""

import bpy
import bmesh
import mathutils
from bpy.types import Operator
from bpy.props import StringProperty
from . import retopo_algorithms
from . import retopo_utils

class MESH_OT_auto_retopo(Operator):
    """Automatically retopologize the selected mesh"""
    bl_idname = "mesh.auto_retopo"
    bl_label = "Auto Retopology"
    bl_description = "Automatically retopologize the selected mesh using the chosen algorithm"
    bl_options = {'REGISTER', 'UNDO'}
    
    preset: StringProperty(
        name="Preset",
        description="Preset configuration",
        default=""
    )
    
    @classmethod
    def poll(cls, context):
        """Check if operator can be executed"""
        return (context.active_object is not None and 
                context.active_object.type == 'MESH' and
                context.mode == 'OBJECT')
    
    def execute(self, context):
        """Execute the retopology operation"""
        obj = context.active_object
        scene = context.scene
        props = scene.retopo_props
        
        # Apply preset if specified
        if self.preset:
            self.apply_preset(props, self.preset)
        
        # Validate input
        if not obj or obj.type != 'MESH':
            self.report({'ERROR'}, "Please select a mesh object")
            return {'CANCELLED'}
        
        if len(obj.data.vertices) == 0:
            self.report({'ERROR'}, "Selected mesh has no vertices")
            return {'CANCELLED'}
        
        try:
            # Store original mesh for undo
            original_mesh = obj.data.copy()
            
            # Perform retopology based on selected algorithm
            if props.algorithm == 'QUAD_REMESH':
                success = retopo_algorithms.quad_remesh(obj, props)
            elif props.algorithm == 'VOXEL_REMESH':
                success = retopo_algorithms.voxel_remesh(obj, props)
            elif props.algorithm == 'EDGE_FLOW':
                success = retopo_algorithms.edge_flow_retopo(obj, props)
            elif props.algorithm == 'ADAPTIVE':
                success = retopo_algorithms.adaptive_retopo(obj, props)
            else:
                self.report({'ERROR'}, f"Unknown algorithm: {props.algorithm}")
                return {'CANCELLED'}
            
            if not success:
                # Restore original mesh if retopology failed
                obj.data = original_mesh
                self.report({'ERROR'}, "Retopology failed")
                return {'CANCELLED'}
            
            # Apply post-processing
            self.post_process(obj, props)
            
            # Update viewport
            context.view_layer.update()
            
            self.report({'INFO'}, f"Retopology completed using {props.algorithm}")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Retopology failed: {str(e)}")
            return {'CANCELLED'}
    
    def apply_preset(self, props, preset):
        """Apply preset configuration"""
        if preset == 'LOW_POLY':
            props.target_face_count = 500
            props.algorithm = 'QUAD_REMESH'
            props.quad_octree_depth = 3
            props.preserve_sharp_edges = True

        elif preset == 'HIGH_DETAIL':
            props.target_face_count = 5000
            props.algorithm = 'ADAPTIVE'
            props.preserve_sharp_edges = True
            props.voxel_adaptivity = 0.3

        elif preset == 'GAME_ASSET':
            props.target_face_count = 1000
            props.algorithm = 'QUAD_REMESH'
            props.quad_octree_depth = 4
            props.preserve_sharp_edges = True
            props.smooth_shading = False

        elif preset == 'ANIMATION':
            props.target_face_count = 3000
            props.algorithm = 'EDGE_FLOW'
            props.edge_flow_iterations = 4
            props.preserve_sharp_edges = False
            props.smooth_shading = True
    
    def post_process(self, obj, props):
        """Apply post-processing to the retopologized mesh"""
        # Enter edit mode to clean up mesh
        bpy.context.view_layer.objects.active = obj
        bpy.ops.object.mode_set(mode='EDIT')
        
        # Select all
        bpy.ops.mesh.select_all(action='SELECT')
        
        # Remove doubles
        bpy.ops.mesh.remove_doubles(threshold=0.001)
        
        # Recalculate normals
        bpy.ops.mesh.normals_make_consistent(inside=False)
        
        # Apply smooth shading if requested
        if props.smooth_shading:
            bpy.ops.mesh.faces_shade_smooth()
        else:
            bpy.ops.mesh.faces_shade_flat()
        
        # Return to object mode
        bpy.ops.object.mode_set(mode='OBJECT')

class MESH_OT_analyze_mesh_quality(Operator):
    """Analyze mesh quality and topology"""
    bl_idname = "mesh.analyze_mesh_quality"
    bl_label = "Analyze Mesh Quality"
    bl_description = "Analyze the quality and topology of the selected mesh"
    bl_options = {'REGISTER'}
    
    @classmethod
    def poll(cls, context):
        """Check if operator can be executed"""
        return (context.active_object is not None and 
                context.active_object.type == 'MESH')
    
    def execute(self, context):
        """Execute the quality analysis"""
        obj = context.active_object
        
        if not obj or obj.type != 'MESH':
            self.report({'ERROR'}, "Please select a mesh object")
            return {'CANCELLED'}
        
        try:
            # Analyze mesh quality
            quality_metrics = retopo_utils.analyze_mesh_quality(obj)
            
            # Store results on object
            obj.retopo_quality = quality_metrics
            
            # Report results
            quad_ratio = quality_metrics.get('quad_ratio', 0)
            triangle_count = quality_metrics.get('triangle_count', 0)
            ngon_count = quality_metrics.get('ngon_count', 0)
            edge_flow_score = quality_metrics.get('edge_flow_score', 0)
            
            self.report({'INFO'}, 
                       f"Quality Analysis: {quad_ratio:.1%} quads, "
                       f"{triangle_count} triangles, {ngon_count} n-gons, "
                       f"Edge flow: {edge_flow_score:.2f}")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Quality analysis failed: {str(e)}")
            return {'CANCELLED'}

class MESH_OT_compare_meshes(Operator):
    """Compare original and retopologized meshes"""
    bl_idname = "mesh.compare_meshes"
    bl_label = "Compare Meshes"
    bl_description = "Compare quality metrics between original and retopologized meshes"
    bl_options = {'REGISTER'}

    @classmethod
    def poll(cls, context):
        """Check if operator can be executed"""
        return (context.active_object is not None and
                context.active_object.type == 'MESH')

    def execute(self, context):
        """Execute the mesh comparison"""
        obj = context.active_object

        if not obj or obj.type != 'MESH':
            self.report({'ERROR'}, "Please select a mesh object")
            return {'CANCELLED'}

        try:
            # Find original mesh (look for backup or similar named object)
            original_obj = None
            obj_name = obj.name

            # Look for common backup naming patterns
            backup_names = [
                f"{obj_name}_original",
                f"{obj_name}_backup",
                f"{obj_name}.001",
                obj_name.replace("_retopo", ""),
                obj_name.replace("_low", "_high"),
            ]

            for backup_name in backup_names:
                if backup_name in bpy.data.objects:
                    candidate = bpy.data.objects[backup_name]
                    if candidate.type == 'MESH' and candidate != obj:
                        original_obj = candidate
                        break

            if not original_obj:
                self.report({'WARNING'}, "No original mesh found for comparison")
                return {'CANCELLED'}

            # Analyze both meshes
            current_quality = retopo_utils.analyze_mesh_quality(obj)
            original_quality = retopo_utils.analyze_mesh_quality(original_obj)

            # Calculate improvements
            face_reduction = (1.0 - current_quality['total_faces'] / max(1, original_quality['total_faces'])) * 100
            quad_improvement = current_quality['quad_ratio'] - original_quality['quad_ratio']

            # Store comparison results
            obj.retopo_comparison = {
                'original_faces': original_quality['total_faces'],
                'current_faces': current_quality['total_faces'],
                'face_reduction': face_reduction,
                'quad_improvement': quad_improvement,
                'original_quad_ratio': original_quality['quad_ratio'],
                'current_quad_ratio': current_quality['quad_ratio']
            }

            # Report results
            self.report({'INFO'},
                       f"Comparison: {face_reduction:.1f}% face reduction, "
                       f"Quad ratio: {original_quality['quad_ratio']:.1%} → {current_quality['quad_ratio']:.1%}")

            return {'FINISHED'}

        except Exception as e:
            self.report({'ERROR'}, f"Mesh comparison failed: {str(e)}")
            return {'CANCELLED'}
