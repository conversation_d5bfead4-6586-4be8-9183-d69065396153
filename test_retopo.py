"""
Test script for Auto Retopology Addon
Run this script in Blender to test the addon functionality
"""

import bpy
import bmesh
import mathutils
from mathutils import Vector
import time

def create_test_meshes():
    """Create various test meshes for retopology testing"""
    
    # Clear existing mesh objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    test_meshes = []
    
    # 1. High-poly sphere (organic shape test)
    bpy.ops.mesh.primitive_uv_sphere_add(radius=2, location=(0, 0, 0))
    sphere = bpy.context.active_object
    sphere.name = "Test_Sphere_HighPoly"
    
    # Subdivide to make it high-poly
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.subdivide(number_cuts=3)
    bpy.ops.object.mode_set(mode='OBJECT')
    
    test_meshes.append(sphere)
    
    # 2. Cube with beveled edges (hard surface test)
    bpy.ops.mesh.primitive_cube_add(size=2, location=(5, 0, 0))
    cube = bpy.context.active_object
    cube.name = "Test_Cube_Beveled"
    
    # Add bevel modifier
    bevel_mod = cube.modifiers.new(name="Bevel", type='BEVEL')
    bevel_mod.width = 0.1
    bevel_mod.segments = 3
    bpy.ops.object.modifier_apply(modifier=bevel_mod.name)
    
    test_meshes.append(cube)
    
    # 3. Suzanne (complex organic test)
    bpy.ops.mesh.primitive_monkey_add(size=2, location=(-5, 0, 0))
    suzanne = bpy.context.active_object
    suzanne.name = "Test_Suzanne"
    
    # Subdivide Suzanne
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.subdivide(number_cuts=2)
    bpy.ops.object.mode_set(mode='OBJECT')
    
    test_meshes.append(suzanne)
    
    # 4. Torus (mixed curvature test)
    bpy.ops.mesh.primitive_torus_add(
        major_radius=1.5, 
        minor_radius=0.5, 
        location=(0, 5, 0)
    )
    torus = bpy.context.active_object
    torus.name = "Test_Torus"
    
    # Subdivide torus
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.subdivide(number_cuts=2)
    bpy.ops.object.mode_set(mode='OBJECT')
    
    test_meshes.append(torus)
    
    return test_meshes

def test_algorithm(obj, algorithm, target_faces=1000):
    """Test a specific retopology algorithm on an object"""
    
    print(f"\n=== Testing {algorithm} on {obj.name} ===")
    
    # Store original stats
    original_verts = len(obj.data.vertices)
    original_faces = len(obj.data.polygons)
    
    print(f"Original: {original_verts} vertices, {original_faces} faces")
    
    # Set up retopo properties
    scene = bpy.context.scene
    if not hasattr(scene, 'retopo_props'):
        print("ERROR: Retopo properties not found. Is the addon enabled?")
        return False
    
    props = scene.retopo_props
    props.algorithm = algorithm
    props.target_face_count = target_faces
    props.preserve_sharp_edges = True
    props.smooth_shading = True
    
    # Algorithm-specific settings
    if algorithm == 'QUAD_REMESH':
        props.quad_octree_depth = 4
        props.quad_scale = 0.99
    elif algorithm == 'VOXEL_REMESH':
        props.voxel_size = 0.1
        props.voxel_adaptivity = 0.0
    elif algorithm == 'EDGE_FLOW':
        props.edge_flow_iterations = 3
    
    # Select the object
    bpy.context.view_layer.objects.active = obj
    obj.select_set(True)
    
    # Record start time
    start_time = time.time()
    
    try:
        # Run retopology
        result = bpy.ops.mesh.auto_retopo()
        
        if result == {'FINISHED'}:
            # Record end time
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Get new stats
            new_verts = len(obj.data.vertices)
            new_faces = len(obj.data.polygons)
            
            print(f"Result: {new_verts} vertices, {new_faces} faces")
            print(f"Processing time: {processing_time:.2f} seconds")
            print(f"Face reduction: {((original_faces - new_faces) / original_faces * 100):.1f}%")
            
            # Analyze quality
            bpy.ops.mesh.analyze_mesh_quality()
            
            if hasattr(obj, 'retopo_quality'):
                quality = obj.retopo_quality
                print(f"Quality metrics:")
                print(f"  Quad ratio: {quality.get('quad_ratio', 0):.1%}")
                print(f"  Edge flow score: {quality.get('edge_flow_score', 0):.2f}")
                print(f"  Overall quality: {quality.get('overall_quality', 0):.2f}")
                print(f"  Quality grade: {quality.get('quality_grade', 'N/A')}")
            
            return True
            
        else:
            print(f"ERROR: Retopology failed with result: {result}")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception during retopology: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive test suite"""
    
    print("=" * 60)
    print("AUTO RETOPOLOGY ADDON - COMPREHENSIVE TEST")
    print("=" * 60)
    
    # Check if addon is enabled
    if 'auto_retopology' not in bpy.context.preferences.addons:
        print("ERROR: Auto Retopology addon is not enabled!")
        print("Please enable the addon in Preferences > Add-ons")
        return
    
    # Create test meshes
    print("\nCreating test meshes...")
    test_meshes = create_test_meshes()
    print(f"Created {len(test_meshes)} test meshes")
    
    # Test algorithms
    algorithms = ['QUAD_REMESH', 'VOXEL_REMESH', 'EDGE_FLOW', 'ADAPTIVE']
    target_face_counts = [500, 1000, 2000]
    
    results = {}
    
    for mesh in test_meshes:
        results[mesh.name] = {}
        
        for algorithm in algorithms:
            results[mesh.name][algorithm] = {}
            
            for target_faces in target_face_counts:
                # Duplicate mesh for testing
                bpy.context.view_layer.objects.active = mesh
                mesh.select_set(True)
                bpy.ops.object.duplicate()
                test_obj = bpy.context.active_object
                test_obj.name = f"{mesh.name}_{algorithm}_{target_faces}"
                
                # Test the algorithm
                success = test_algorithm(test_obj, algorithm, target_faces)
                results[mesh.name][algorithm][target_faces] = success
                
                # Deselect
                test_obj.select_set(False)
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    total_tests = 0
    successful_tests = 0
    
    for mesh_name, mesh_results in results.items():
        print(f"\n{mesh_name}:")
        for algorithm, algo_results in mesh_results.items():
            success_count = sum(1 for success in algo_results.values() if success)
            total_count = len(algo_results)
            print(f"  {algorithm}: {success_count}/{total_count} successful")
            
            total_tests += total_count
            successful_tests += success_count
    
    print(f"\nOverall success rate: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
    
    if successful_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
    elif successful_tests > total_tests * 0.8:
        print("✅ Most tests passed - addon is working well")
    elif successful_tests > total_tests * 0.5:
        print("⚠️  Some tests failed - check for issues")
    else:
        print("❌ Many tests failed - addon needs debugging")

def quick_test():
    """Quick test with default cube"""
    
    print("Running quick test...")
    
    # Clear scene
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Add default cube
    bpy.ops.mesh.primitive_cube_add()
    cube = bpy.context.active_object
    
    # Subdivide to make it more interesting
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.subdivide(number_cuts=2)
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Test quad remesh
    success = test_algorithm(cube, 'QUAD_REMESH', 500)
    
    if success:
        print("✅ Quick test PASSED")
    else:
        print("❌ Quick test FAILED")

if __name__ == "__main__":
    # Run quick test by default
    # Uncomment the line below for comprehensive testing
    quick_test()
    # run_comprehensive_test()
