"""
Test script to demonstrate the non-destructive retopology workflow
"""

import bpy

def test_non_destructive_retopo():
    """Test the non-destructive retopology feature"""
    
    print("Testing Non-Destructive Retopology...")
    print("=" * 50)
    
    # Clear existing objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Create a high-poly test mesh
    bpy.ops.mesh.primitive_uv_sphere_add()
    obj = bpy.context.active_object
    obj.name = "HighPoly_Sphere"
    
    # Subdivide to create high-poly mesh
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.subdivide(number_cuts=3)  # Creates lots of faces
    bpy.ops.object.mode_set(mode='OBJECT')
    
    original_faces = len(obj.data.polygons)
    original_verts = len(obj.data.vertices)
    
    print(f"Created high-poly mesh: {original_verts} vertices, {original_faces} faces")
    
    # Set up retopology settings
    scene = bpy.context.scene
    props = scene.retopo_props
    
    # Test non-destructive mode (default)
    props.preserve_original = True
    props.target_face_count = 500  # Much lower face count
    props.algorithm = 'VOXEL_REMESH'
    
    print(f"Target face count: {props.target_face_count}")
    print(f"Preserve original: {props.preserve_original}")
    
    try:
        # Run retopology
        result = bpy.ops.mesh.auto_retopo()
        
        if result == {'FINISHED'}:
            print("✓ Retopology completed successfully!")
            
            # Check that we now have two objects
            objects_in_scene = [obj for obj in bpy.context.scene.objects if obj.type == 'MESH']
            print(f"Objects in scene: {len(objects_in_scene)}")
            
            # Find the original and retopo objects
            original_obj = None
            retopo_obj = None
            
            for obj in objects_in_scene:
                if "_Original" in obj.name:
                    original_obj = obj
                elif "_Retopo" in obj.name:
                    retopo_obj = obj
            
            if original_obj and retopo_obj:
                print("✓ Both original and retopo objects found!")
                
                # Check face counts
                orig_faces = len(original_obj.data.polygons)
                retopo_faces = len(retopo_obj.data.polygons)
                
                print(f"Original mesh: {orig_faces} faces")
                print(f"Retopo mesh: {retopo_faces} faces")
                
                # Check that original is unchanged
                if orig_faces == original_faces:
                    print("✓ Original mesh preserved unchanged!")
                else:
                    print(f"✗ Original mesh changed! Expected {original_faces}, got {orig_faces}")
                
                # Check that retopo has fewer faces
                if retopo_faces < orig_faces:
                    reduction = ((orig_faces - retopo_faces) / orig_faces * 100)
                    print(f"✓ Retopo mesh has {reduction:.1f}% fewer faces!")
                else:
                    print("⚠ Retopo mesh doesn't have fewer faces")
                
                # Check positioning
                distance = (retopo_obj.location - original_obj.location).length
                if distance > 0:
                    print(f"✓ Objects positioned apart (distance: {distance:.2f})")
                else:
                    print("⚠ Objects are at the same location")
                
                return True
            else:
                print("✗ Could not find both original and retopo objects")
                return False
        else:
            print(f"✗ Retopology failed: {result}")
            return False
            
    except Exception as e:
        print(f"✗ Retopology failed with error: {e}")
        return False

def test_destructive_mode():
    """Test the destructive retopology mode"""
    
    print("\nTesting Destructive Mode...")
    print("=" * 30)
    
    # Clear existing objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Create test mesh
    bpy.ops.mesh.primitive_cube_add()
    obj = bpy.context.active_object
    obj.name = "TestCube"
    
    # Subdivide
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.subdivide(number_cuts=2)
    bpy.ops.object.mode_set(mode='OBJECT')
    
    original_faces = len(obj.data.polygons)
    print(f"Original mesh: {original_faces} faces")
    
    # Set up destructive mode
    scene = bpy.context.scene
    props = scene.retopo_props
    props.preserve_original = False  # Destructive mode
    props.target_face_count = 50
    props.algorithm = 'VOXEL_REMESH'
    
    print(f"Preserve original: {props.preserve_original}")
    
    try:
        # Run retopology
        result = bpy.ops.mesh.auto_retopo()
        
        if result == {'FINISHED'}:
            print("✓ Destructive retopology completed!")
            
            # Check that we still have only one object
            objects_in_scene = [obj for obj in bpy.context.scene.objects if obj.type == 'MESH']
            
            if len(objects_in_scene) == 1:
                print("✓ Only one object remains (destructive mode working)")
                
                final_faces = len(objects_in_scene[0].data.polygons)
                print(f"Final mesh: {final_faces} faces")
                
                if final_faces != original_faces:
                    print("✓ Original mesh was modified (destructive mode)")
                    return True
                else:
                    print("⚠ Mesh wasn't changed")
                    return False
            else:
                print(f"✗ Expected 1 object, found {len(objects_in_scene)}")
                return False
        else:
            print(f"✗ Destructive retopology failed: {result}")
            return False
            
    except Exception as e:
        print(f"✗ Destructive retopology failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing Non-Destructive Retopology Feature")
    print("=" * 60)
    
    # Check if addon is enabled
    if 'auto_retopology' not in bpy.context.preferences.addons:
        print("ERROR: Auto Retopology addon is not enabled!")
        print("Please enable the addon in Preferences > Add-ons")
    else:
        test1 = test_non_destructive_retopo()
        test2 = test_destructive_mode()
        
        print("\n" + "=" * 60)
        print("SUMMARY:")
        print(f"Non-Destructive Mode: {'✓ PASS' if test1 else '✗ FAIL'}")
        print(f"Destructive Mode: {'✓ PASS' if test2 else '✗ FAIL'}")
        
        if test1 and test2:
            print("\n🎉 Both modes working correctly!")
            print("\nHow to use:")
            print("• Check 'Preserve Original' for non-destructive workflow (recommended)")
            print("• Uncheck 'Preserve Original' to modify the original mesh directly")
        else:
            print("\n⚠ Some tests failed. Check the output above for details.")
