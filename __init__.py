"""
Auto Retopology Addon for Blender
Provides instant retopology with multiple algorithms for mesh optimization
"""

bl_info = {
    "name": "Auto Retopology",
    "author": "AI Assistant",
    "version": (1, 0, 0),
    "blender": (4, 0, 0),
    "location": "View3D > Sidebar > Retopo Tab",
    "description": "Instant retopology with multiple algorithms for mesh optimization",
    "category": "Mesh",
    "doc_url": "",
    "tracker_url": "",
}

import bpy
import bmesh
import mathutils
from bpy.types import (
    Panel,
    Operator,
    PropertyGroup,
    AddonPreferences,
)
from bpy.props import (
    FloatProperty,
    IntProperty,
    BoolProperty,
    EnumProperty,
    PointerProperty,
)

# Import our modules
from . import retopo_operators
from . import retopo_ui
from . import retopo_algorithms
from . import retopo_utils

# Classes to register
classes = [
    retopo_operators.MESH_OT_auto_retopo,
    retopo_operators.MESH_OT_analyze_mesh_quality,
    retopo_operators.MESH_OT_compare_meshes,
    retopo_ui.RETOPO_PT_main_panel,
    retopo_ui.RETOPO_PT_settings_panel,
    retopo_ui.RETOPO_PT_quality_panel,
    retopo_ui.RetopoProperties,
]

def register():
    """Register all classes and properties"""
    for cls in classes:
        bpy.utils.register_class(cls)
    
    # Add properties to scene
    bpy.types.Scene.retopo_props = PointerProperty(type=retopo_ui.RetopoProperties)
    
    print("Auto Retopology addon registered successfully")

def unregister():
    """Unregister all classes and properties"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
    
    # Remove properties from scene
    if hasattr(bpy.types.Scene, 'retopo_props'):
        del bpy.types.Scene.retopo_props
    
    print("Auto Retopology addon unregistered")

if __name__ == "__main__":
    register()
