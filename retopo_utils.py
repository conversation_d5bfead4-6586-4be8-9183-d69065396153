"""
Utility functions for Auto Retopology Addon
"""

import bpy
import bmesh
import mathutils
from mathutils import Vector
import math

def analyze_mesh_quality(obj):
    """
    Analyze mesh quality and return metrics
    """
    mesh = obj.data
    bm = bmesh.new()
    bm.from_mesh(mesh)
    
    # Ensure face indices are valid
    bm.faces.ensure_lookup_table()
    bm.edges.ensure_lookup_table()
    bm.verts.ensure_lookup_table()
    
    # Count face types
    triangle_count = 0
    quad_count = 0
    ngon_count = 0
    
    for face in bm.faces:
        if len(face.verts) == 3:
            triangle_count += 1
        elif len(face.verts) == 4:
            quad_count += 1
        else:
            ngon_count += 1
    
    total_faces = len(bm.faces)
    quad_ratio = quad_count / total_faces if total_faces > 0 else 0.0
    
    # Calculate edge flow score
    edge_flow_score = calculate_edge_flow_score(bm)

    # Calculate aspect ratio quality
    aspect_ratio_score = calculate_aspect_ratio_score(bm)

    # Calculate edge length consistency
    edge_length_score = calculate_edge_length_score(bm)

    # Calculate additional quality metrics
    manifold_score = calculate_manifold_score(bm)
    topology_score = calculate_topology_score(bm)
    density_uniformity = calculate_density_uniformity(bm)

    # Free BMesh after all calculations are done
    bm.free()

    # Calculate weighted overall quality
    weights = {
        'quad_ratio': 0.3,
        'edge_flow': 0.25,
        'aspect_ratio': 0.2,
        'edge_length': 0.15,
        'manifold': 0.05,
        'topology': 0.05
    }

    overall_quality = (
        quad_ratio * weights['quad_ratio'] +
        edge_flow_score * weights['edge_flow'] +
        aspect_ratio_score * weights['aspect_ratio'] +
        edge_length_score * weights['edge_length'] +
        manifold_score * weights['manifold'] +
        topology_score * weights['topology']
    )

    return {
        'quad_ratio': quad_ratio,
        'triangle_count': triangle_count,
        'quad_count': quad_count,
        'ngon_count': ngon_count,
        'total_faces': total_faces,
        'edge_flow_score': edge_flow_score,
        'aspect_ratio_score': aspect_ratio_score,
        'edge_length_score': edge_length_score,
        'manifold_score': manifold_score,
        'topology_score': topology_score,
        'density_uniformity': density_uniformity,
        'overall_quality': overall_quality,
        'quality_grade': get_quality_grade(overall_quality)
    }

def calculate_edge_flow_score(bm):
    """
    Calculate edge flow quality score (0-1, higher is better)
    """
    if len(bm.edges) == 0:
        return 0.0
    
    flow_score = 0.0
    edge_count = 0
    
    for edge in bm.edges:
        if len(edge.link_faces) == 2:
            # Calculate angle between adjacent faces
            face1, face2 = edge.link_faces
            angle = face1.normal.angle(face2.normal)
            
            # Good edge flow has consistent angles
            # Penalize very sharp or very flat angles
            if angle < math.pi / 6:  # Less than 30 degrees
                score = angle / (math.pi / 6)
            elif angle > 5 * math.pi / 6:  # More than 150 degrees
                score = (math.pi - angle) / (math.pi / 6)
            else:
                score = 1.0
            
            flow_score += score
            edge_count += 1
    
    return flow_score / edge_count if edge_count > 0 else 0.0

def calculate_aspect_ratio_score(bm):
    """
    Calculate aspect ratio quality score (0-1, higher is better)
    """
    if len(bm.faces) == 0:
        return 0.0
    
    aspect_score = 0.0
    
    for face in bm.faces:
        if len(face.verts) < 3:
            continue
        
        # Calculate face aspect ratio
        edge_lengths = []
        for edge in face.edges:
            edge_lengths.append(edge.calc_length())
        
        if not edge_lengths:
            continue
        
        min_edge = min(edge_lengths)
        max_edge = max(edge_lengths)
        
        if min_edge > 0:
            aspect_ratio = min_edge / max_edge
            aspect_score += aspect_ratio
    
    return aspect_score / len(bm.faces)

def calculate_edge_length_score(bm):
    """
    Calculate edge length consistency score (0-1, higher is better)
    """
    if len(bm.edges) == 0:
        return 0.0
    
    edge_lengths = [edge.calc_length() for edge in bm.edges]
    
    if not edge_lengths:
        return 0.0
    
    avg_length = sum(edge_lengths) / len(edge_lengths)
    
    # Calculate coefficient of variation
    variance = sum((length - avg_length) ** 2 for length in edge_lengths) / len(edge_lengths)
    std_dev = math.sqrt(variance)
    
    if avg_length > 0:
        cv = std_dev / avg_length
        # Convert to score (lower CV is better)
        score = max(0.0, 1.0 - cv)
    else:
        score = 0.0
    
    return score

def get_mesh_bounds(obj):
    """
    Get bounding box dimensions of mesh
    """
    bbox_corners = [obj.matrix_world @ Vector(corner) for corner in obj.bound_box]
    
    min_x = min(corner.x for corner in bbox_corners)
    max_x = max(corner.x for corner in bbox_corners)
    min_y = min(corner.y for corner in bbox_corners)
    max_y = max(corner.y for corner in bbox_corners)
    min_z = min(corner.z for corner in bbox_corners)
    max_z = max(corner.z for corner in bbox_corners)
    
    return {
        'min': Vector((min_x, min_y, min_z)),
        'max': Vector((max_x, max_y, max_z)),
        'size': Vector((max_x - min_x, max_y - min_y, max_z - min_z)),
        'center': Vector(((min_x + max_x) / 2, (min_y + max_y) / 2, (min_z + max_z) / 2))
    }

def calculate_optimal_voxel_size(obj, target_faces):
    """
    Calculate optimal voxel size for target face count
    """
    bounds = get_mesh_bounds(obj)
    max_dimension = max(bounds['size'])

    if max_dimension <= 0 or target_faces <= 0:
        return 0.1

    # Better formula: voxel size should be inversely related to target face density
    # Higher face count = smaller voxels = more detail
    # Approximate relationship: face_count ≈ (max_dimension / voxel_size)^2

    # Calculate voxel size to achieve target face count
    # face_count ≈ (max_dimension / voxel_size)^2
    # voxel_size ≈ max_dimension / sqrt(face_count)

    # Add scaling factor to better match real-world results
    scaling_factor = 2.0  # Empirically determined
    voxel_size = (max_dimension * scaling_factor) / (target_faces ** 0.5)

    # More conservative clamping to prevent deformation
    min_safe_voxel = max_dimension / 50.0   # Prevent too many faces/deformation
    max_safe_voxel = max_dimension / 3.0    # Prevent too few faces

    voxel_size = max(min_safe_voxel, min(max_safe_voxel, voxel_size))

    # Absolute minimum to prevent crashes
    voxel_size = max(0.01, voxel_size)

    print(f"Voxel calculation: mesh_size={max_dimension:.3f}, target_faces={target_faces}, voxel_size={voxel_size:.4f}")

    return voxel_size

def calculate_optimal_octree_depth(obj, target_faces):
    """
    Calculate optimal octree depth for target face count
    """
    bounds = get_mesh_bounds(obj)
    max_dimension = max(bounds['size'])

    if max_dimension <= 0 or target_faces <= 0:
        return 4

    # Better formula: octree depth should increase with target face count
    # Each octree level roughly quadruples the face count (2^2 in each dimension)
    # face_count ≈ 4^depth (very rough approximation)
    # depth ≈ log4(face_count) = log2(face_count) / 2

    # Calculate base depth from face count
    base_depth = math.log2(max(1, target_faces)) / 2.0

    # Add offset and scaling for better results
    # Empirically determined values
    depth = int(base_depth * 0.4 + 2)

    # Clamp to reasonable range
    return max(1, min(10, depth))

def validate_mesh(obj):
    """
    Validate mesh for retopology operations
    """
    if not obj or obj.type != 'MESH':
        return False, "Object is not a mesh"
    
    mesh = obj.data
    if len(mesh.vertices) == 0:
        return False, "Mesh has no vertices"
    
    if len(mesh.polygons) == 0:
        return False, "Mesh has no faces"
    
    # Check for non-manifold geometry
    bm = bmesh.new()
    bm.from_mesh(mesh)
    
    non_manifold_edges = [edge for edge in bm.edges if not edge.is_manifold]
    non_manifold_verts = [vert for vert in bm.verts if not vert.is_manifold]
    
    bm.free()
    
    if non_manifold_edges or non_manifold_verts:
        return False, f"Mesh has non-manifold geometry: {len(non_manifold_edges)} edges, {len(non_manifold_verts)} vertices"
    
    return True, "Mesh is valid"

def prepare_mesh_for_retopo(obj):
    """
    Prepare mesh for retopology by cleaning up common issues
    """
    # Enter edit mode
    bpy.context.view_layer.objects.active = obj
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Select all
    bpy.ops.mesh.select_all(action='SELECT')
    
    # Remove doubles
    bpy.ops.mesh.remove_doubles(threshold=0.001)
    
    # Fill holes
    bpy.ops.mesh.fill_holes(sides=0)
    
    # Recalculate normals
    bpy.ops.mesh.normals_make_consistent(inside=False)
    
    # Return to object mode
    bpy.ops.object.mode_set(mode='OBJECT')

def create_retopo_material():
    """
    Create a material for visualizing retopology results
    """
    mat_name = "Retopo_Wireframe"
    
    # Check if material already exists
    if mat_name in bpy.data.materials:
        return bpy.data.materials[mat_name]
    
    # Create new material
    mat = bpy.data.materials.new(name=mat_name)
    mat.use_nodes = True
    
    # Clear default nodes
    mat.node_tree.nodes.clear()
    
    # Add nodes
    output_node = mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
    bsdf_node = mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
    wireframe_node = mat.node_tree.nodes.new(type='ShaderNodeWireframe')
    
    # Set up wireframe visualization
    bsdf_node.inputs['Base Color'].default_value = (0.2, 0.8, 0.2, 1.0)  # Green
    bsdf_node.inputs['Metallic'].default_value = 0.0
    bsdf_node.inputs['Roughness'].default_value = 0.8
    
    # Connect nodes
    mat.node_tree.links.new(wireframe_node.outputs['Fac'], bsdf_node.inputs['Alpha'])
    mat.node_tree.links.new(bsdf_node.outputs['BSDF'], output_node.inputs['Surface'])
    
    # Set blend mode
    mat.blend_method = 'BLEND'
    
    return mat

def calculate_manifold_score(bm):
    """
    Calculate manifold quality score (0-1, higher is better)
    """
    if len(bm.edges) == 0:
        return 1.0

    manifold_edges = sum(1 for edge in bm.edges if edge.is_manifold)
    manifold_ratio = manifold_edges / len(bm.edges)

    return manifold_ratio

def calculate_topology_score(bm):
    """
    Calculate topology quality score based on vertex valence distribution
    """
    if len(bm.verts) == 0:
        return 1.0

    # Count vertex valences
    valence_counts = {}
    for vert in bm.verts:
        valence = len(vert.link_edges)
        valence_counts[valence] = valence_counts.get(valence, 0) + 1

    # Ideal valence distribution for quad meshes
    # Most vertices should have valence 4, some 3 and 5 are acceptable
    total_verts = len(bm.verts)
    ideal_score = 0.0

    for valence, count in valence_counts.items():
        ratio = count / total_verts
        if valence == 4:
            ideal_score += ratio * 1.0  # Perfect
        elif valence in [3, 5]:
            ideal_score += ratio * 0.8  # Good
        elif valence in [2, 6]:
            ideal_score += ratio * 0.5  # Acceptable
        else:
            ideal_score += ratio * 0.2  # Poor

    return ideal_score

def calculate_density_uniformity(bm):
    """
    Calculate mesh density uniformity score
    """
    if len(bm.faces) == 0:
        return 1.0

    # Calculate face areas
    face_areas = [face.calc_area() for face in bm.faces]

    if not face_areas:
        return 1.0

    # Calculate coefficient of variation
    mean_area = sum(face_areas) / len(face_areas)
    if mean_area > 0:
        variance = sum((area - mean_area) ** 2 for area in face_areas) / len(face_areas)
        std_dev = math.sqrt(variance)
        cv = std_dev / mean_area

        # Convert to score (lower CV is better)
        uniformity_score = max(0.0, 1.0 - cv)
    else:
        uniformity_score = 0.0

    return uniformity_score

def get_quality_grade(overall_quality):
    """
    Convert quality score to letter grade
    """
    if overall_quality >= 0.9:
        return 'A+'
    elif overall_quality >= 0.8:
        return 'A'
    elif overall_quality >= 0.7:
        return 'B'
    elif overall_quality >= 0.6:
        return 'C'
    elif overall_quality >= 0.5:
        return 'D'
    else:
        return 'F'

def analyze_mesh_problems(obj):
    """
    Analyze mesh for common topology problems
    """
    mesh = obj.data
    bm = bmesh.new()
    bm.from_mesh(mesh)

    bm.faces.ensure_lookup_table()
    bm.edges.ensure_lookup_table()
    bm.verts.ensure_lookup_table()

    problems = []

    # Check for non-manifold geometry
    non_manifold_edges = [edge for edge in bm.edges if not edge.is_manifold]
    non_manifold_verts = [vert for vert in bm.verts if not vert.is_manifold]

    if non_manifold_edges:
        problems.append(f"Non-manifold edges: {len(non_manifold_edges)}")

    if non_manifold_verts:
        problems.append(f"Non-manifold vertices: {len(non_manifold_verts)}")

    # Check for degenerate faces
    degenerate_faces = []
    for face in bm.faces:
        if face.calc_area() < 0.000001:
            degenerate_faces.append(face)

    if degenerate_faces:
        problems.append(f"Degenerate faces: {len(degenerate_faces)}")

    # Check for isolated vertices
    isolated_verts = [vert for vert in bm.verts if len(vert.link_edges) == 0]
    if isolated_verts:
        problems.append(f"Isolated vertices: {len(isolated_verts)}")

    # Check for very long/short edges
    edge_lengths = [edge.calc_length() for edge in bm.edges]
    if edge_lengths:
        mean_length = sum(edge_lengths) / len(edge_lengths)
        very_short = sum(1 for length in edge_lengths if length < mean_length * 0.1)
        very_long = sum(1 for length in edge_lengths if length > mean_length * 5.0)

        if very_short > 0:
            problems.append(f"Very short edges: {very_short}")
        if very_long > 0:
            problems.append(f"Very long edges: {very_long}")

    bm.free()

    return problems

def suggest_improvements(quality_metrics):
    """
    Suggest improvements based on quality analysis
    """
    suggestions = []

    quad_ratio = quality_metrics.get('quad_ratio', 0)
    if quad_ratio < 0.7:
        suggestions.append("Consider using Quad Remesh algorithm for better quad topology")
        suggestions.append("Try increasing octree depth or decreasing voxel size")

    edge_flow_score = quality_metrics.get('edge_flow_score', 0)
    if edge_flow_score < 0.6:
        suggestions.append("Use Edge Flow algorithm to improve edge flow")
        suggestions.append("Increase edge flow iterations")

    aspect_ratio_score = quality_metrics.get('aspect_ratio_score', 0)
    if aspect_ratio_score < 0.7:
        suggestions.append("Faces have poor aspect ratios - try different algorithm")
        suggestions.append("Consider manual cleanup of stretched faces")

    manifold_score = quality_metrics.get('manifold_score', 1)
    if manifold_score < 0.95:
        suggestions.append("Mesh has non-manifold geometry - run mesh cleanup")
        suggestions.append("Check for holes or overlapping faces")

    return suggestions
