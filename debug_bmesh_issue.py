"""
Debug script to check the B<PERSON>esh issue and verify the fix is loaded
"""

import bpy
import inspect

def check_analyze_mesh_quality_code():
    """Check the actual code of analyze_mesh_quality function"""
    
    try:
        # Import the retopo_utils module
        from . import retopo_utils
        
        # Get the source code of the function
        source = inspect.getsource(retopo_utils.analyze_mesh_quality)
        
        print("Current analyze_mesh_quality function code:")
        print("=" * 60)
        print(source)
        print("=" * 60)
        
        # Check if the fix is present
        if "# Free BMesh after all calculations are done" in source:
            print("✓ Fix is present in the code")
            
            # Check if bm.free() is in the right place
            lines = source.split('\n')
            free_line = -1
            manifold_line = -1
            topology_line = -1
            
            for i, line in enumerate(lines):
                if "bm.free()" in line:
                    free_line = i
                elif "manifold_score = calculate_manifold_score(bm)" in line:
                    manifold_line = i
                elif "topology_score = calculate_topology_score(bm)" in line:
                    topology_line = i
            
            if free_line > manifold_line and free_line > topology_line:
                print("✓ bm.free() is correctly placed after all calculations")
                return True
            else:
                print("✗ bm.free() is in the wrong position!")
                print(f"  bm.free() at line {free_line}")
                print(f"  manifold_score calculation at line {manifold_line}")
                print(f"  topology_score calculation at line {topology_line}")
                return False
        else:
            print("✗ Fix is NOT present in the loaded code")
            print("This suggests the addon needs to be reloaded")
            return False
            
    except Exception as e:
        print(f"✗ Error checking code: {e}")
        return False

def test_bmesh_issue():
    """Test if the BMesh issue still occurs"""
    
    print("\nTesting BMesh issue...")
    
    # Create a simple test mesh
    bpy.ops.mesh.primitive_cube_add()
    obj = bpy.context.active_object
    
    try:
        from . import retopo_utils
        
        # Try to analyze mesh quality
        quality_metrics = retopo_utils.analyze_mesh_quality(obj)
        
        print("✓ analyze_mesh_quality completed successfully!")
        print(f"  Quad ratio: {quality_metrics.get('quad_ratio', 0):.2%}")
        print(f"  Total faces: {quality_metrics.get('total_faces', 0)}")
        
        # Clean up
        bpy.ops.object.delete()
        return True
        
    except Exception as e:
        print(f"✗ analyze_mesh_quality failed: {e}")
        
        # Clean up
        bpy.ops.object.delete()
        return False

def check_addon_reload_needed():
    """Check if the addon needs to be reloaded"""
    
    print("\nChecking if addon reload is needed...")
    
    # Check if the addon is enabled
    addon_name = 'auto_retopology'  # Adjust this to match your addon name
    
    if addon_name in bpy.context.preferences.addons:
        print(f"✓ Addon '{addon_name}' is enabled")
        
        # Check the addon's bl_info
        try:
            addon_module = bpy.context.preferences.addons[addon_name].module
            if hasattr(addon_module, 'bl_info'):
                version = addon_module.bl_info.get('version', 'Unknown')
                print(f"  Addon version: {version}")
            
            print("\nTo reload the addon:")
            print("1. Go to Edit > Preferences > Add-ons")
            print(f"2. Find '{addon_name}' and disable it")
            print("3. Enable it again")
            print("4. Or restart Blender")
            
        except Exception as e:
            print(f"  Error checking addon info: {e}")
    else:
        print(f"✗ Addon '{addon_name}' is not enabled!")
        print("Please enable the addon first")

if __name__ == "__main__":
    print("BMesh Issue Debug Script")
    print("=" * 50)
    
    # Check the code
    code_ok = check_analyze_mesh_quality_code()
    
    # Test the issue
    test_ok = test_bmesh_issue()
    
    # Check if reload is needed
    check_addon_reload_needed()
    
    print("\n" + "=" * 50)
    if code_ok and test_ok:
        print("✓ Everything looks good! The fix is working.")
    elif code_ok and not test_ok:
        print("✗ Code looks correct but test failed. There might be another issue.")
    elif not code_ok:
        print("✗ The fix is not loaded. Please reload the addon.")
    
    print("\nIf you're still getting the BMesh error:")
    print("1. Make sure to reload/restart the addon")
    print("2. Check the Blender console for detailed error messages")
    print("3. Try restarting Blender completely")
