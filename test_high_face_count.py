"""
Test script to verify high face count limits work correctly
"""

import bpy

def test_high_face_count_limits():
    """Test that high face count values can be set and used"""
    
    # Get the retopo properties
    scene = bpy.context.scene
    if not hasattr(scene, 'retopo_props'):
        print("✗ Retopo properties not found. Make sure the addon is enabled.")
        return False
    
    props = scene.retopo_props
    
    # Test various high face count values
    test_values = [
        10000,      # 10K faces
        50000,      # 50K faces  
        100000,     # 100K faces
        500000,     # 500K faces
        1000000,    # 1M faces
        5000000,    # 5M faces
        10000000,   # 10M faces (maximum)
    ]
    
    print("Testing high face count limits...")
    print("=" * 50)
    
    success_count = 0
    for test_value in test_values:
        try:
            # Try to set the face count
            props.target_face_count = test_value
            
            # Verify it was set correctly
            if props.target_face_count == test_value:
                print(f"✓ {test_value:,} faces: Successfully set")
                success_count += 1
            else:
                print(f"✗ {test_value:,} faces: Set to {props.target_face_count:,} instead")
                
        except Exception as e:
            print(f"✗ {test_value:,} faces: Error - {e}")
    
    print("=" * 50)
    print(f"Results: {success_count}/{len(test_values)} tests passed")
    
    # Test the preset values
    print("\nTesting preset values...")
    
    # Test High Detail preset
    try:
        bpy.ops.mesh.auto_retopo(preset='HIGH_DETAIL')
        if props.target_face_count == 20000:
            print("✓ High Detail preset: 20,000 faces")
        else:
            print(f"✗ High Detail preset: Expected 20,000, got {props.target_face_count:,}")
    except Exception as e:
        print(f"✗ High Detail preset: Error - {e}")
    
    return success_count == len(test_values)

def test_ui_display():
    """Test that the UI properly displays high face counts"""
    
    scene = bpy.context.scene
    props = scene.retopo_props
    
    # Create a test mesh to see face count display
    bpy.ops.mesh.primitive_cube_add()
    obj = bpy.context.active_object
    
    # Test with a high face count setting
    props.target_face_count = 100000
    
    print(f"\nUI Display Test:")
    print(f"Current mesh faces: {len(obj.data.polygons):,}")
    print(f"Target face count: {props.target_face_count:,}")
    
    # Calculate reduction/increase percentage (same logic as UI)
    if props.target_face_count > 0:
        reduction = (1.0 - props.target_face_count / max(1, len(obj.data.polygons))) * 100
        if reduction > 0:
            print(f"Reduction: {reduction:.1f}%")
        else:
            print(f"Increase: {-reduction:.1f}%")
    
    # Clean up
    bpy.ops.object.delete()
    
    return True

if __name__ == "__main__":
    print("Testing High Face Count Limits...")
    print("=" * 60)
    
    test1_passed = test_high_face_count_limits()
    test2_passed = test_ui_display()
    
    print("=" * 60)
    if test1_passed and test2_passed:
        print("✓ All high face count tests passed!")
        print("Users can now set face counts up to 10 million.")
    else:
        print("✗ Some tests failed. Please check the implementation.")
