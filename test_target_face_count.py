"""
Test script to verify that target face count is properly respected
"""

import bpy
import time

def test_target_face_count_accuracy():
    """Test that algorithms produce face counts close to the target"""
    
    # Clear existing objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Create a test mesh (subdivided cube for more interesting topology)
    bpy.ops.mesh.primitive_cube_add()
    obj = bpy.context.active_object
    obj.name = "TestMesh"
    
    # Subdivide to create more geometry
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.subdivide(number_cuts=2)
    bpy.ops.object.mode_set(mode='OBJECT')
    
    original_faces = len(obj.data.polygons)
    print(f"Original mesh: {original_faces} faces")
    
    # Test different target face counts
    test_targets = [500, 1000, 2000, 5000, 10000]
    algorithms = ['QUAD_REMESH', 'VOXEL_REMESH', 'EDGE_FLOW', 'ADAPTIVE']
    
    results = {}
    
    for algorithm in algorithms:
        results[algorithm] = {}
        print(f"\n=== Testing {algorithm} ===")
        
        for target_faces in test_targets:
            # Duplicate the original mesh for testing
            bpy.context.view_layer.objects.active = obj
            obj.select_set(True)
            bpy.ops.object.duplicate()
            test_obj = bpy.context.active_object
            test_obj.name = f"Test_{algorithm}_{target_faces}"
            
            # Set up properties
            scene = bpy.context.scene
            props = scene.retopo_props
            props.algorithm = algorithm
            props.target_face_count = target_faces
            props.preserve_sharp_edges = True
            props.smooth_shading = True
            
            # Algorithm-specific settings
            if algorithm == 'QUAD_REMESH':
                props.quad_octree_depth = 4  # Will be overridden by calculation
                props.quad_scale = 0.99
            elif algorithm == 'VOXEL_REMESH':
                props.voxel_size = 0.1  # Will be overridden by calculation
                props.voxel_adaptivity = 0.0
            elif algorithm == 'EDGE_FLOW':
                props.edge_flow_iterations = 3
            
            # Record start time
            start_time = time.time()
            
            try:
                # Run retopology
                result = bpy.ops.mesh.auto_retopo()
                
                if result == {'FINISHED'}:
                    # Record results
                    end_time = time.time()
                    processing_time = end_time - start_time
                    actual_faces = len(test_obj.data.polygons)
                    
                    # Calculate accuracy
                    if target_faces > 0:
                        accuracy = (1.0 - abs(actual_faces - target_faces) / target_faces) * 100
                    else:
                        accuracy = 0
                    
                    results[algorithm][target_faces] = {
                        'actual_faces': actual_faces,
                        'accuracy': accuracy,
                        'processing_time': processing_time,
                        'success': True
                    }
                    
                    print(f"  Target: {target_faces:5d} → Actual: {actual_faces:5d} "
                          f"(Accuracy: {accuracy:5.1f}%, Time: {processing_time:.2f}s)")
                    
                else:
                    results[algorithm][target_faces] = {
                        'actual_faces': 0,
                        'accuracy': 0,
                        'processing_time': 0,
                        'success': False
                    }
                    print(f"  Target: {target_faces:5d} → FAILED")
                    
            except Exception as e:
                results[algorithm][target_faces] = {
                    'actual_faces': 0,
                    'accuracy': 0,
                    'processing_time': 0,
                    'success': False,
                    'error': str(e)
                }
                print(f"  Target: {target_faces:5d} → ERROR: {e}")
            
            # Deselect test object
            test_obj.select_set(False)
    
    # Print summary
    print("\n" + "="*80)
    print("ACCURACY SUMMARY")
    print("="*80)
    
    for algorithm in algorithms:
        print(f"\n{algorithm}:")
        total_accuracy = 0
        successful_tests = 0
        
        for target_faces in test_targets:
            result = results[algorithm][target_faces]
            if result['success']:
                print(f"  {target_faces:5d} faces: {result['accuracy']:5.1f}% accuracy "
                      f"({result['actual_faces']:5d} actual)")
                total_accuracy += result['accuracy']
                successful_tests += 1
            else:
                print(f"  {target_faces:5d} faces: FAILED")
        
        if successful_tests > 0:
            avg_accuracy = total_accuracy / successful_tests
            print(f"  Average accuracy: {avg_accuracy:.1f}%")
        else:
            print(f"  Average accuracy: 0% (all tests failed)")
    
    return results

if __name__ == "__main__":
    print("Testing Target Face Count Accuracy...")
    print("="*80)
    
    # Check if addon is enabled
    if 'auto_retopology' not in bpy.context.preferences.addons:
        print("ERROR: Auto Retopology addon is not enabled!")
        print("Please enable the addon in Preferences > Add-ons")
    else:
        results = test_target_face_count_accuracy()
        
        print("\n" + "="*80)
        print("Test completed! Check the results above to see how accurately")
        print("each algorithm matches the target face count.")
