"""
Test script to verify the bar deformation fix
"""

import bpy
from mathutils import Vector

def test_cube_retopology():
    """Test retopology on a simple cube to check for bar deformation"""
    
    print("Testing Cube Retopology (Bar Deformation Fix)")
    print("=" * 60)
    
    # Clear existing objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Create a standard cube
    bpy.ops.mesh.primitive_cube_add(size=2.0)
    obj = bpy.context.active_object
    obj.name = "TestCube"
    
    # Get original dimensions
    original_bounds = [obj.matrix_world @ Vector(corner) for corner in obj.bound_box]
    original_size = Vector([
        max(corner.x for corner in original_bounds) - min(corner.x for corner in original_bounds),
        max(corner.y for corner in original_bounds) - min(corner.y for corner in original_bounds),
        max(corner.z for corner in original_bounds) - min(corner.z for corner in original_bounds)
    ])
    
    print(f"Original cube size: {original_size.x:.3f} x {original_size.y:.3f} x {original_size.z:.3f}")
    print(f"Original faces: {len(obj.data.polygons)}")
    
    # Configure retopology settings
    scene = bpy.context.scene
    props = scene.retopo_props
    
    # Test different target face counts
    test_cases = [
        (100, "Very Low Poly"),
        (500, "Low Poly"), 
        (1000, "Medium Poly"),
        (2000, "High Poly")
    ]
    
    results = []
    
    for target_faces, description in test_cases:
        print(f"\n{'-' * 40}")
        print(f"Testing: {description} ({target_faces} faces)")
        print(f"{'-' * 40}")
        
        # Duplicate the original cube for this test
        bpy.context.view_layer.objects.active = obj
        obj.select_set(True)
        bpy.ops.object.duplicate()
        test_obj = bpy.context.active_object
        test_obj.name = f"Test_{target_faces}_faces"
        
        # Configure settings
        props.algorithm = 'VOXEL_REMESH'
        props.target_face_count = target_faces
        props.preserve_original = False  # Work directly on duplicate
        props.preserve_shape = False     # Test without shape preservation first
        
        try:
            # Run retopology
            result = bpy.ops.mesh.auto_retopo()
            
            if result == {'FINISHED'}:
                # Check the result
                final_faces = len(test_obj.data.polygons)
                
                # Get new dimensions
                new_bounds = [test_obj.matrix_world @ Vector(corner) for corner in test_obj.bound_box]
                new_size = Vector([
                    max(corner.x for corner in new_bounds) - min(corner.x for corner in new_bounds),
                    max(corner.y for corner in new_bounds) - min(corner.y for corner in new_bounds),
                    max(corner.z for corner in new_bounds) - min(corner.z for corner in new_bounds)
                ])
                
                print(f"Result size: {new_size.x:.3f} x {new_size.y:.3f} x {new_size.z:.3f}")
                print(f"Result faces: {final_faces}")
                
                # Check for bar deformation
                # Calculate aspect ratios
                dimensions = [new_size.x, new_size.y, new_size.z]
                dimensions = [d for d in dimensions if d > 0.001]  # Filter out near-zero dimensions
                
                if len(dimensions) >= 2:
                    max_dim = max(dimensions)
                    min_dim = min(dimensions)
                    aspect_ratio = max_dim / min_dim if min_dim > 0 else float('inf')
                    
                    print(f"Aspect ratio: {aspect_ratio:.1f}")
                    
                    # Determine result quality
                    if aspect_ratio <= 3.0:
                        status = "✓ EXCELLENT - Good proportions maintained"
                        quality = "excellent"
                    elif aspect_ratio <= 6.0:
                        status = "✓ GOOD - Acceptable proportions"
                        quality = "good"
                    elif aspect_ratio <= 15.0:
                        status = "⚠ POOR - Some deformation"
                        quality = "poor"
                    else:
                        status = "✗ FAILED - Severe bar deformation"
                        quality = "failed"
                    
                    print(status)
                    
                    # Check face count accuracy
                    face_accuracy = (1.0 - abs(final_faces - target_faces) / target_faces) * 100
                    print(f"Face count accuracy: {face_accuracy:.1f}%")
                    
                    results.append({
                        'target_faces': target_faces,
                        'description': description,
                        'final_faces': final_faces,
                        'aspect_ratio': aspect_ratio,
                        'quality': quality,
                        'face_accuracy': face_accuracy,
                        'success': True
                    })
                    
                else:
                    print("✗ FAILED - Invalid geometry (missing dimensions)")
                    results.append({
                        'target_faces': target_faces,
                        'description': description,
                        'quality': 'failed',
                        'success': False
                    })
                    
            else:
                print(f"✗ FAILED - Retopology operation failed: {result}")
                results.append({
                    'target_faces': target_faces,
                    'description': description,
                    'quality': 'failed',
                    'success': False
                })
                
        except Exception as e:
            print(f"✗ FAILED - Exception: {e}")
            results.append({
                'target_faces': target_faces,
                'description': description,
                'quality': 'failed',
                'success': False
            })
    
    # Summary
    print("\n" + "=" * 60)
    print("BAR DEFORMATION FIX TEST RESULTS")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['success']]
    
    if successful_tests:
        print("\nResults Summary:")
        for result in results:
            if result['success']:
                print(f"{result['description']:15} | "
                      f"Faces: {result['final_faces']:4d} | "
                      f"Ratio: {result['aspect_ratio']:5.1f} | "
                      f"Quality: {result['quality'].upper()}")
            else:
                print(f"{result['description']:15} | FAILED")
        
        # Overall assessment
        excellent_count = sum(1 for r in successful_tests if r['quality'] == 'excellent')
        good_count = sum(1 for r in successful_tests if r['quality'] == 'good')
        poor_count = sum(1 for r in successful_tests if r['quality'] == 'poor')
        failed_count = len(results) - len(successful_tests)
        
        print(f"\nOverall Results:")
        print(f"  Excellent: {excellent_count}")
        print(f"  Good: {good_count}")
        print(f"  Poor: {poor_count}")
        print(f"  Failed: {failed_count}")
        
        if excellent_count + good_count >= len(results) * 0.75:
            print("\n🎉 SUCCESS! Bar deformation issue appears to be fixed!")
            print("The retopology is maintaining proper proportions.")
        elif excellent_count + good_count >= len(results) * 0.5:
            print("\n✓ IMPROVED! Most tests show better results.")
            print("Some cases may still need fine-tuning.")
        else:
            print("\n⚠ MIXED RESULTS! Some improvement but issues remain.")
            print("May need further adjustments to voxel size calculation.")
    else:
        print("\n✗ ALL TESTS FAILED!")
        print("The retopology system needs more work.")
    
    print(f"\nTested with {len(results)} different face count targets.")
    print("Check the 3D viewport to visually inspect the results.")

if __name__ == "__main__":
    print("Bar Deformation Fix Test")
    print("=" * 60)
    
    # Check if addon is enabled
    if 'auto_retopology' not in bpy.context.preferences.addons:
        print("ERROR: Auto Retopology addon is not enabled!")
        print("Please enable the addon in Preferences > Add-ons")
    else:
        test_cube_retopology()
