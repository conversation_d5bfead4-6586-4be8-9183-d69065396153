"""
Debug script to diagnose retopology issues
"""

import bpy
from mathutils import Vector

def debug_retopo_issue():
    """Debug the retopology bar/deformation issue"""
    
    print("Debugging Retopology Issue...")
    print("=" * 50)
    
    # Clear existing objects
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    # Create a simple test cube
    bpy.ops.mesh.primitive_cube_add(size=2.0)
    obj = bpy.context.active_object
    obj.name = "TestCube"
    
    print(f"Created test cube: {obj.name}")
    print(f"Original vertices: {len(obj.data.vertices)}")
    print(f"Original faces: {len(obj.data.polygons)}")
    
    # Get original bounding box
    original_bbox = [obj.matrix_world @ Vector(corner) for corner in obj.bound_box]
    original_size = Vector([
        max(corner.x for corner in original_bbox) - min(corner.x for corner in original_bbox),
        max(corner.y for corner in original_bbox) - min(corner.y for corner in original_bbox),
        max(corner.z for corner in original_bbox) - min(corner.z for corner in original_bbox)
    ])
    
    print(f"Original size: {original_size.x:.3f} x {original_size.y:.3f} x {original_size.z:.3f}")
    
    # Test with shape preservation OFF first
    scene = bpy.context.scene
    props = scene.retopo_props
    
    print("\n" + "=" * 30)
    print("Testing WITHOUT Shape Preservation")
    print("=" * 30)
    
    # Configure settings
    props.preserve_original = True  # Keep original for comparison
    props.preserve_shape = False    # Turn OFF shape preservation
    props.target_face_count = 100   # Low face count
    props.algorithm = 'VOXEL_REMESH'
    
    print(f"Settings:")
    print(f"  Algorithm: {props.algorithm}")
    print(f"  Target faces: {props.target_face_count}")
    print(f"  Preserve original: {props.preserve_original}")
    print(f"  Preserve shape: {props.preserve_shape}")
    
    try:
        # Run retopology
        result = bpy.ops.mesh.auto_retopo()
        
        if result == {'FINISHED'}:
            print("✓ Retopology completed!")
            
            # Find the retopo object
            retopo_obj = None
            original_obj = None
            
            for obj in bpy.context.scene.objects:
                if obj.type == 'MESH':
                    if "_Retopo" in obj.name:
                        retopo_obj = obj
                    elif "_Original" in obj.name:
                        original_obj = obj
            
            if retopo_obj:
                print(f"Retopo object: {retopo_obj.name}")
                print(f"Retopo vertices: {len(retopo_obj.data.vertices)}")
                print(f"Retopo faces: {len(retopo_obj.data.polygons)}")
                
                # Check bounding box
                retopo_bbox = [retopo_obj.matrix_world @ Vector(corner) for corner in retopo_obj.bound_box]
                retopo_size = Vector([
                    max(corner.x for corner in retopo_bbox) - min(corner.x for corner in retopo_bbox),
                    max(corner.y for corner in retopo_bbox) - min(corner.y for corner in retopo_bbox),
                    max(corner.z for corner in retopo_bbox) - min(corner.z for corner in retopo_bbox)
                ])
                
                print(f"Retopo size: {retopo_size.x:.3f} x {retopo_size.y:.3f} x {retopo_size.z:.3f}")
                
                # Check if it's deformed into a bar
                size_ratios = [
                    retopo_size.x / max(retopo_size.y, retopo_size.z, 0.001),
                    retopo_size.y / max(retopo_size.x, retopo_size.z, 0.001),
                    retopo_size.z / max(retopo_size.x, retopo_size.y, 0.001)
                ]
                
                max_ratio = max(size_ratios)
                if max_ratio > 10:
                    print(f"⚠ WARNING: Object appears to be deformed into a bar! Ratio: {max_ratio:.1f}")
                    print("This suggests the retopology algorithm is failing")
                else:
                    print(f"✓ Object shape looks reasonable. Max ratio: {max_ratio:.1f}")
                
                # Check vertex positions
                vertex_positions = [v.co for v in retopo_obj.data.vertices]
                if vertex_positions:
                    min_pos = Vector([min(v.x for v in vertex_positions), 
                                    min(v.y for v in vertex_positions), 
                                    min(v.z for v in vertex_positions)])
                    max_pos = Vector([max(v.x for v in vertex_positions), 
                                    max(v.y for v in vertex_positions), 
                                    max(v.z for v in vertex_positions)])
                    
                    print(f"Vertex range: {min_pos} to {max_pos}")
                
            else:
                print("✗ Could not find retopo object")
                
        else:
            print(f"✗ Retopology failed: {result}")
            
    except Exception as e:
        print(f"✗ Error during retopology: {e}")
        import traceback
        traceback.print_exc()
    
    # Now test with different voxel sizes
    print("\n" + "=" * 30)
    print("Testing Different Voxel Sizes")
    print("=" * 30)
    
    # Clear and create fresh cube
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False)
    
    bpy.ops.mesh.primitive_cube_add(size=2.0)
    obj = bpy.context.active_object
    obj.name = "TestCube2"
    
    # Test different voxel sizes
    voxel_sizes = [0.1, 0.2, 0.5]
    
    for voxel_size in voxel_sizes:
        print(f"\nTesting voxel size: {voxel_size}")
        
        # Duplicate the cube for this test
        bpy.context.view_layer.objects.active = obj
        obj.select_set(True)
        bpy.ops.object.duplicate()
        test_obj = bpy.context.active_object
        test_obj.name = f"Test_Voxel_{voxel_size}"
        
        # Apply voxel remesh manually
        try:
            modifier = test_obj.modifiers.new(name="TestVoxel", type='REMESH')
            modifier.mode = 'VOXEL'
            modifier.voxel_size = voxel_size
            modifier.adaptivity = 0.0  # No adaptivity
            
            bpy.context.view_layer.objects.active = test_obj
            bpy.ops.object.modifier_apply(modifier=modifier.name)
            
            # Check result
            faces = len(test_obj.data.polygons)
            bbox = [test_obj.matrix_world @ Vector(corner) for corner in test_obj.bound_box]
            size = Vector([
                max(corner.x for corner in bbox) - min(corner.x for corner in bbox),
                max(corner.y for corner in bbox) - min(corner.y for corner in bbox),
                max(corner.z for corner in bbox) - min(corner.z for corner in bbox)
            ])
            
            print(f"  Result: {faces} faces, size: {size.x:.3f}x{size.y:.3f}x{size.z:.3f}")
            
            # Check if deformed
            size_ratios = [
                size.x / max(size.y, size.z, 0.001),
                size.y / max(size.x, size.z, 0.001),
                size.z / max(size.x, size.y, 0.001)
            ]
            max_ratio = max(size_ratios)
            
            if max_ratio > 10:
                print(f"  ⚠ DEFORMED! Ratio: {max_ratio:.1f}")
            else:
                print(f"  ✓ Good shape. Ratio: {max_ratio:.1f}")
                
        except Exception as e:
            print(f"  ✗ Failed: {e}")

def check_addon_settings():
    """Check current addon settings"""
    
    print("\nCurrent Addon Settings:")
    print("=" * 30)
    
    scene = bpy.context.scene
    if hasattr(scene, 'retopo_props'):
        props = scene.retopo_props
        
        print(f"Algorithm: {props.algorithm}")
        print(f"Target face count: {props.target_face_count}")
        print(f"Preserve original: {props.preserve_original}")
        print(f"Preserve shape: {props.preserve_shape}")
        print(f"Voxel size: {props.voxel_size}")
        print(f"Voxel adaptivity: {props.voxel_adaptivity}")
        print(f"Auto optimize: {props.auto_optimize}")
        
    else:
        print("✗ Retopo properties not found - addon may not be loaded")

if __name__ == "__main__":
    print("Retopology Issue Debug Script")
    print("=" * 60)
    
    # Check if addon is enabled
    if 'auto_retopology' not in bpy.context.preferences.addons:
        print("ERROR: Auto Retopology addon is not enabled!")
        print("Please enable the addon in Preferences > Add-ons")
    else:
        check_addon_settings()
        debug_retopo_issue()
        
        print("\n" + "=" * 60)
        print("DEBUG COMPLETE")
        print("If you see 'DEFORMED' warnings, the issue is with the voxel remesh algorithm.")
        print("Try using a larger voxel size or different algorithm.")
